{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.4.0", "@radix-ui/react-slot": "^1.2.3", "@react-oauth/google": "^0.12.1", "@tailwindcss/vite": "^4.1.4", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.9.2", "html2canvas": "^1.4.1", "http": "^0.0.1-security", "install": "^0.13.0", "jspdf": "^3.0.1", "katex": "^0.16.22", "lucide-react": "^0.503.0", "nodemailer": "^7.0.3", "npm": "^11.3.0", "openai": "^4.96.0", "react": "^18.3.1", "react-audio-visualize": "^1.2.0", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-player": "^2.16.0", "react-router-dom": "^7.6.1", "react-speech-recognition": "^4.0.1", "react-tooltip": "^5.28.1", "react-typed": "^2.0.12", "regenerator-runtime": "^0.14.1", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "swiper": "^11.2.6", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.4", "wavesurfer.js": "^7.9.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "bootstrap": "^5.3.2", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "microsoft-cognitiveservices-speech-sdk": "^1.44.0", "vite": "^6.3.1"}}