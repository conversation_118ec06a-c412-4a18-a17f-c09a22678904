// src/components/SpeechRecognizer.js
import React, { useState } from 'react';
import * as Speech<PERSON><PERSON> from 'microsoft-cognitiveservices-speech-sdk';

const SpeechRecognizer = () => {
  const [transcript, setTranscript] = useState('');
  const [recognizing, setRecognizing] = useState(false);

  const subscriptionKey = '9WXoqH8D1RqplfFGNLg3yQFCBY5IaZdxNMkAQqJJfGftyICMKyuuJQQJ99BEACqBBLyXJ3w3AAAYACOGaS3g';
  const serviceRegion = 'southeastasia'; // e.g., 'eastus'

  const startRecognition = () => {
    const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(subscriptionKey, serviceRegion);
    speechConfig.speechRecognitionLanguage = 'en-IN';

    const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
    const recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);

    setRecognizing(true);
    setTranscript('');

    recognizer.recognizing = (s, e) => {
      console.log('Recognizing:', e.result.text);
    };

    recognizer.recognized = (s, e) => {
      if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech) {
        setTranscript(prev => prev + ' ' + e.result.text);
      } else if (e.result.reason === SpeechSDK.ResultReason.NoMatch) {
        console.log('No speech recognized.');
      }
    };

    recognizer.canceled = (s, e) => {
      console.error('Canceled:', e);
      recognizer.stopContinuousRecognitionAsync();
      setRecognizing(false);
    };

    recognizer.sessionStopped = () => {
      recognizer.stopContinuousRecognitionAsync();
      setRecognizing(false);
    };

    recognizer.startContinuousRecognitionAsync();
  };

  return (
    <div className="p-4">
      <button
        onClick={startRecognition}
        disabled={recognizing}
        className="bg-blue-500 text-white px-4 py-2 rounded"
      >
        {recognizing ? 'Listening...' : 'Start Speech Recognition'}
      </button>
      <div className="mt-4 border p-2 rounded bg-gray-100">
        <p className="font-semibold">Transcript:</p>
        <p>{transcript}</p>
      </div>
    </div>
  );
};

export default SpeechRecognizer;
