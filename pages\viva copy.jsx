// Viva.jsx
import React, { useState, useRef, useEffect } from "react";
import * as SpeechSD<PERSON> from "microsoft-cognitiveservices-speech-sdk";
import { MdOutlineDownloadForOffline } from "react-icons/md";
import { HiRefresh } from "react-icons/hi";
import { IoCloseCircleOutline } from "react-icons/io5";
import { FaMicrophone } from "react-icons/fa";
import { IoIosVideocam } from "react-icons/io";
import { IoSend } from "react-icons/io5";
import PreferenceModal from "./modals/PreferenceModal";
import { createAvatarSynthesizer, createWebRTCConnection } from "./Utility";
import { avatarAppConfig } from "./config";
import axios from "axios";

/* ────────────────────────────────────────────────────────────────────────── */
/*  🔈 1.  PUT YOUR SPEECH KEY & REGION IN ENV VARIABLES (recommended)       */
/* ------------------------------------------------------------------------ */
const SPEECH_KEY = import.meta.env.VITE_AZURE_SPEECH_KEY;      // or a hard-coded test key
const SPEECH_REGION = import.meta.env.VITE_AZURE_SPEECH_REGION; // e.g. "eastus"

/* ────────────────────────────────────────────────────────────────────────── */
const Viva = () => {
    /* Chat state ----------------------------------------------------------- */
    const [avatarSynthesizer, setAvatarSynthesizer] = useState(null);
    const [interviewStarted, setInterviewStarted] = useState(false);
    const [language, setLanguage] = useState("hindi");
    const [topic, setTopic] = useState("General Knowledge");
    const [input, setInput] = useState("");
    const [messages, setMessages] = useState([
        {
            from: "ai",
            text: "Hey! No worries—we can turn this around. First, tell me: what topics are covered in the exam?",
        },
        { from: "user", text: "Viva" },
    ]);

    /* Preferences ---------------------------------------------------------- */
    useEffect(() => {
        const stored = JSON.parse(localStorage.getItem("userVivaPreference"));
        if (stored) {
            setTopic(stored.topic);
            setLanguage(stored.language);
        }
    }, []);

    /* Webcam refs ---------------------------------------------------------- */
    const [webcamOn, setWebcamOn] = useState(false);
    const videoRef = useRef(null);
    const streamRef = useRef(null);
    const avatarVideoRef = useRef();
    const avatarAudioRef = useRef();

    /* Azure Speech recognizer refs ---------------------------------------- */
    const recognizerRef = useRef(null);
    const [micActive, setMicActive] = useState(false);

    /* Modal state ---------------------------------------------------------- */
    const [showPreferenceModal, setShowPreferenceModal] = useState(true);
    const [initialPref, setInitialPref] = useState({ topic: "", language: "" });

    /* Text-to-speech helper ----------------------------------------------- */
    const speakText = (text) =>
        new Promise((resolve, reject) => {
            if (!avatarSynthesizer) return reject("Avatar not ready.");
            avatarSynthesizer.speakTextAsync(
                text,
                (r) =>
                    r.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted
                        ? resolve()
                        : reject("Synthesis failed."),
                reject
            );
        });

    /* Start avatar video/audio session ------------------------------------ */
    const startSession = async () => {
        const pc = createWebRTCConnection(
            avatarAppConfig.iceUrl,
            avatarAppConfig.iceUsername,
            avatarAppConfig.iceCredential
        );
        pc.ontrack = handleOnTrack;
        pc.addTransceiver("video", { direction: "sendrecv" });
        pc.addTransceiver("audio", { direction: "sendrecv" });

        const avatar = createAvatarSynthesizer({
            voice: language === "hindi" ? "hi-IN-SwaraNeural" : "en-IN-NeerjaNeural",
        });
        await avatar.startAvatarAsync(pc);
        setAvatarSynthesizer(avatar);
        setInterviewStarted(true);
    };

    const handleOnTrack = (event) => {
        if (event.track.kind === "video") {
            avatarVideoRef.current.srcObject = event.streams[0];
        } else if (event.track.kind === "audio") {
            avatarAudioRef.current.srcObject = event.streams[0];
            avatarAudioRef.current.muted = false;
            avatarAudioRef.current.play().catch(() => { });
        }
    };

    /* ───── 2. Ask OpenAI (same as before) ───────────────────────────────── */
    const askOpenAi = async (userInput) => {
        setMessages((m) => [...m, { from: "user", text: userInput }]);
        try {
            const res = await axios.post(
                "https://api.openai.com/v1/chat/completions",
                {
                    model: "gpt-3.5-turbo",
                    messages: [
                        {
                            role: "system",
                            content: `You are a professional interviewer. Ask one question at a time about "${topic}". The last answer is: ${messages[messages.length - 1]?.text || "none"
                                }`,
                        },
                        { role: "user", content: userInput },
                    ],
                    temperature: 0,
                },
                {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`,
                    },
                }
            );

            const reply = res.data.choices[0].message.content;
            setMessages((m) => [...m, { from: "ai", text: reply }]);
            await speakText(reply);
        } catch (err) {
            console.error("OpenAI error:", err.response?.data || err);
        }
    };

    /* ───── 3. Azure Speech Recognition logic ───────────────────────────── */
    const startAzureMic = () => {
        /* configure recognizer */
        const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
            SPEECH_KEY,
            SPEECH_REGION
        );
        speechConfig.speechRecognitionLanguage = "en-IN";

        const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
        const recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);

        recognizer.recognizing = (_, e) => {
            /* live stream while speaking */
            setInput(e.result.text);
        };

        recognizer.recognized = (_, e) => {
            if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech) {
                const finalText = e.result.text.trim();
                setInput(finalText);
                if (finalText) handleSend(finalText); // auto-send on final result
            }
        };

        recognizer.canceled = (_, e) => {
            console.error("Recognition canceled:", e);
            recognizer.stopContinuousRecognitionAsync();
            setMicActive(false);
        };

        recognizer.sessionStopped = () => {
            recognizer.stopContinuousRecognitionAsync();
            setMicActive(false);
        };

        recognizer.startContinuousRecognitionAsync();
        recognizerRef.current = recognizer;
    };

    const stopAzureMic = () => {
        recognizerRef.current?.stopContinuousRecognitionAsync(() => {
            recognizerRef.current?.close();
            recognizerRef.current = null;
        });
    };

    /* toggle mic */
    useEffect(() => {
        if (micActive) startAzureMic();
        else stopAzureMic();
        // cleanup on unmount
        return stopAzureMic;
    }, [micActive]);

    /* ───── 4. Send button / Enter key ───────────────────────────────────── */
    const handleSend = async (forcedInput) => {
        const text = (forcedInput ?? input).trim();
        if (!text) return;
        await askOpenAi(text);
        setInput("");
    };

    /* Webcam toggle (unchanged) ------------------------------------------- */
    useEffect(() => {
        if (webcamOn) {
            navigator.mediaDevices
                .getUserMedia({ video: true })
                .then((stream) => {
                    if (videoRef.current) {
                        videoRef.current.srcObject = stream;
                        streamRef.current = stream;
                    }
                })
                .catch(() => setWebcamOn(false));
        } else {
            streamRef.current?.getTracks().forEach((t) => t.stop());
            if (videoRef.current) videoRef.current.srcObject = null;
        }
        return () => streamRef.current?.getTracks().forEach((t) => t.stop());
    }, [webcamOn]);

    /* UI ------------------------------------------------------------------ */
    return (
        <>
            {showPreferenceModal && (
                <PreferenceModal
                    onClose={() => setShowPreferenceModal(false)}
                    onSubmit={(pref) => {
                        localStorage.setItem("userVivaPreference", JSON.stringify(pref));
                        setInitialPref(pref);
                        setShowPreferenceModal(false);
                        setTopic(pref.topic);
                        setLanguage(pref.language);
                        startSession();
                    }}
                    initialTopic={initialPref.topic}
                    initialLanguage={initialPref.language}
                />
            )}

            {!showPreferenceModal && (
                <div className="min-h-screen bg-white flex flex-col items-center justify-center py-8 px-2 md:px-0">
                    {/* Header */}
                    <div className="w-full max-w-5xl flex justify-between items-center mb-8">
                        <h1 className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 font-bold text-3xl md:text-4xl">
                            AI Interview – LIVE
                        </h1>
                        <div className="flex gap-4 text-[#3734FF]">
                            <MdOutlineDownloadForOffline className="w-7 h-7" />
                            <HiRefresh className="w-7 h-7" />
                            <IoCloseCircleOutline className="w-7 h-7" />
                        </div>
                    </div>

                    {/* Main grid */}
                    <div className="w-full max-w-7xl border rounded-2xl p-6 flex flex-col md:flex-row gap-8 bg-[#D9D9D91A]">
                        {/* AI Camera */}
                        <div className="flex-1 flex flex-col items-center">
                            <div className="relative w-[70%] h-full rounded-2xl overflow-hidden border shadow-lg bg-white flex items-center justify-center">
                                <video
                                    ref={avatarVideoRef}
                                    className="w-full h-full object-cover"
                                    autoPlay
                                    muted
                                />
                                <audio ref={avatarAudioRef} />

                                {/* AI message bubble */}
                                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 w-[90%] bg-gradient-to-r from-[#6C9DFF] to-[#755CFF] rounded-lg px-4 py-2 text-white text-sm shadow-lg">
                                    {messages.filter((m) => m.from === "ai").slice(-1)[0]?.text}
                                </div>
                            </div>
                        </div>

                        {/* User Camera & Chat */}
                        <div className="flex-1 flex flex-col items-center">
                            <div className="w-[90%] h-[400px] rounded-2xl overflow-hidden border shadow-lg bg-gray-100 flex items-center justify-center">
                                {webcamOn ? (
                                    <video
                                        ref={videoRef}
                                        autoPlay
                                        muted
                                        className="object-cover w-full h-full"
                                    />
                                ) : (
                                    <span className="text-2xl text-gray-400">Webcam Off</span>
                                )}
                            </div>

                            {/* Mic / Cam buttons */}
                            <div className="flex gap-6 mt-4">
                                <button
                                    onClick={() => setMicActive((s) => !s)}
                                    className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-2xl shadow-md transition ${micActive
                                        ? "bg-gradient-to-r from-[#A723FF] to-[#1A39FF]"
                                        : "bg-red-600"
                                        }`}
                                >
                                    <FaMicrophone className="w-6 h-6" />
                                </button>

                                <button
                                    onClick={() => setWebcamOn((s) => !s)}
                                    className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-2xl shadow-md transition ${webcamOn
                                        ? "bg-gradient-to-r from-[#A723FF] to-[#1A39FF]"
                                        : "bg-red-600"
                                        }`}
                                >
                                    <IoIosVideocam className="w-6 h-6" />
                                </button>
                            </div>

                            {/* Text input */}
                            <div className="w-full flex items-center gap-2 mt-4">
                                <input
                                    type="text"
                                    placeholder="Your Text Here…"
                                    value={input}
                                    onChange={(e) => setInput(e.target.value)}
                                    onKeyDown={(e) => e.key === "Enter" && handleSend()}
                                    disabled={micActive}
                                    className="flex-1 px-4 py-3 rounded-full border focus:outline-none focus:ring-2 focus:ring-purple-200"
                                />
                                <button
                                    onClick={() => handleSend()}
                                    disabled={micActive}
                                    className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-200 to-blue-200 flex items-center justify-center text-xl text-gray-700 hover:text-purple-700"
                                >
                                    <IoSend className="w-6 h-6" />
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default Viva;
