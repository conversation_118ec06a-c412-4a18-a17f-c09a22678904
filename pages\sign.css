/* Global Styles */


/* Container Layout */
.container1 {
    display: flex;
    min-height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    overflow: hidden;
}

.otp-section {
    text-align: center;
    margin-top: 40px;
}

.otp-inputs {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin: 20px 0;
    flex-wrap: wrap;
    /* ensures responsive wrapping */
}

.otp-box {
    width: 60px;
    height: 60px;
    font-size: 28px;
    text-align: center;
    border: 2px solid #ccc;
    border-radius: 10px;
    outline: none;
    transition: all 0.2s ease-in-out;
    background-color: #fff;
}

.otp-box:focus {
    border-color: #007bff;
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.25);
}

.form-container {
    flex: 1;
    background-color: #fff;
    padding: 40px;
    display: flex;
    flex-direction: column;
}

.info-container {
    width: 40%;
    background-color: #f0f4ff;
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
}

/* Form Styles */
.form-content {
    max-width: 500px;
    margin: 0 auto;
}

.form-title {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.form-subtitle {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 15px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #7c4dff;
}

.form-checkbox {
    display: flex;
    align-items: center;
    margin: 20px 0 30px;
}

.form-checkbox input {
    margin-right: 10px;
    width: 18px;
    height: 18px;
}

.form-checkbox span {
    font-size: 14px;
    color: #555;
}

.link {
    color: #7c4dff;
    text-decoration: none;
}

.btn-primary {
    width: 25%;
    padding: 14px;
    background-color: #7c4dff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}


.btn-primary:hover {
    background-color: #6a3de8;
}

.btn-secondary {
    padding: 12px 30px;
    background-color: transparent;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-secondary:hover {
    background-color: #f5f5f5;
}

.button-group {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: 40px;

}

/* Logo and Illustration */
.logo-container {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #7c4dff, #ff4dff);
    border-radius: 50%;
}

.logo span {
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

.illustration {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.illustration img {
    max-width: 100%;
    height: auto;
}

.welcome-text {
    text-align: center;
    margin-top: 20px;
}

.welcome-text h2 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.welcome-text p {
    font-size: 15px;
    color: #666;
    line-height: 1.5;
}

/* Select Styles */
.select-wrapper {
    position: relative;
}

.select-wrapper select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 15px;
    appearance: none;
    background-color: white;
    cursor: pointer;
}

.select-wrapper::after {
    content: "";
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #666;
    pointer-events: none;
}

.mt-40 {
    margin-top: 40px;
}

/* Student Type Cards */
.student-type-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 30px;
}

.student-type-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s;
}

.student-type-card:hover {
    border-color: #7c4dff;
    box-shadow: 0 4px 12px rgba(124, 77, 255, 0.1);
}

.icon-circle {
    width: 60px;
    height: 60px;
    background-color: #f0f0ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
}

.icon {
    width: 30px;
    height: 30px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}

.coaching-icon {
    background-image: url("/images/coaching-icon.png");
}

.school-icon {
    background-image: url("/images/school-icon.png");
}

.independent-icon {
    background-image: url("/images/independent-icon.png");
}

.college-icon {
    background-image: url("/images/college-icon.png");
}

/* Stream and Subject Options */
.stream-options,
.subject-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

.stream-option {
    flex: 1;
    min-width: 120px;
    padding: 12px 20px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
}

.subject-option {
    padding: 8px 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
}

.stream-option:hover,
.subject-option:hover {
    border-color: #7c4dff;
}

.stream-option.selected,
.subject-option.selected {
    background-color: #7c4dff;
    color: white;
    border-color: #7c4dff;
}

.optional {
    font-weight: normal;
    color: #888;
    font-size: 14px;
}

/* Dashboard Styles */
.dashboard-container {
    min-height: 100vh;
    background-color: #f0f4ff;
    padding: 20px;
}

.dashboard-content {
    max-width: 1200px;
    margin: 0 auto;
    background-color: #f0f4ff;
    border-radius: 12px;
    overflow: hidden;
}

.dashboard-header {
    display: flex;
    justify-content: center;
    padding: 20px 0;
}

.dashboard-logo {
    margin: 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 20px;
}

.feature-card {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.small-card {
    display: flex;
    align-items: center;
}

.feature-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 20px;
}

.pdf-icon {
    background-color: #fff0f8;
    background-image: url("/images/pdf-icon.png");
}

.video-icon {
    background-color: #e6f7ff;
    background-image: url("/images/video-icon.png");
}

.topic-icon {
    background-color: #e6fff0;
    background-image: url("/images/topic-icon.png");
}

.feature-text h3 {
    font-size: 16px;
    margin-bottom: 4px;
}

.feature-text p {
    font-size: 12px;
    color: #666;
}

.streak-card {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    grid-column: span 1;
}

.streak-card h3 {
    font-size: 16px;
    margin-bottom: 15px;
}

.streak-content {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.streak-icon {
    width: 40px;
    height: 40px;
    background-color: #fff0e6;
    border-radius: 50%;
    background-image: url("/images/streak-icon.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 20px;
    margin-right: 10px;
}

.streak-days {
    background-color: #ff6b00;
    color: white;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.streak-message {
    font-size: 14px;
    color: #666;
    margin: 10px 0;
}

.streak-progress {
    margin-top: 15px;
}

.progress-bar {
    height: 6px;
    background-color: #eee;
    border-radius: 3px;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    width: 70%;
    background-color: #ff6b00;
    border-radius: 3px;
}

.milestone {
    font-size: 12px;
    color: #888;
}

.progress-card {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.progress-circle {
    position: relative;
    width: 100px;
    height: 100px;
    margin-bottom: 15px;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    font-weight: 700;
    color: #7c4dff;
}

.progress-label {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
}

.dot {
    width: 8px;
    height: 8px;
    background-color: #7c4dff;
    border-radius: 50%;
    margin-right: 6px;
}

.notes-card {
    grid-column: span 1;
}

.ai-notes {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.notes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.notes-header h3 {
    font-size: 16px;
}

.lock-icon {
    width: 20px;
    height: 20px;
    background-image: url("/images/lock-icon.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}

.chat-card {
    background-color: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    grid-column: span 1;
    display: flex;
    flex-direction: column;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.chat-header h3 {
    font-size: 16px;
}

.chat-controls {
    display: flex;
    gap: 8px;
}

.minimize,
.expand {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ddd;
}

.chat-messages {
    flex: 1;
    padding: 10px 0;
    overflow-y: auto;
    max-height: 150px;
}

.message {
    margin-bottom: 10px;
    max-width: 80%;
    padding: 8px 12px;
    border-radius: 12px;
    font-size: 14px;
}

.bot-message {
    background-color: #f0f0f0;
    align-self: flex-start;
}

.user-message {
    background-color: #7c4dff;
    color: white;
    align-self: flex-end;
    margin-left: auto;
}

.chat-input {
    display: flex;
    align-items: center;
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.chat-input input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
}

.send-button {
    width: 30px;
    height: 30px;
    background-color: #7c4dff;
    border: none;
    border-radius: 50%;
    margin-left: 10px;
    cursor: pointer;
    background-image: url("/images/send-icon.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 14px;
}

.dashboard-footer {
    text-align: center;
    padding: 40px 20px;
}

.footer-title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
}

.footer-subtitle {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
}

.start-learning-btn {
    padding: 14px 30px;
    background-color: #7c4dff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.start-learning-btn:hover {
    background-color: #6a3de8;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }

    .info-container {
        width: 100%;
        order: -1;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .streak-card,
    .notes-card,
    .chat-card {
        grid-column: span 1;
    }
}