import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '/src/assets/fonts.css'; // Make sure this path is correct
import axios from 'axios';

const HandwrittenTest = () => {
    const [uploadedFiles, setUploadedFiles] = useState([]);
    const [isMobile, setIsMobile] = useState(false);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [testData, setTestData] = useState({
        userName: '',
        subject: '',
        totalQuestions: 0,
        totalMarks: 0,
        questions: [],
        timeLeft: '00:00:00'
    });
    const navigate = useNavigate();

    const translator = (word1, word2) =>
        localStorage.getItem("lang") && localStorage.getItem("lang").toLowerCase().includes("english")
            ? word1
            : localStorage.getItem("lang")
                ? word2
                : word1;

    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 768);
        };

        handleResize(); // Initial check
        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    useEffect(() => {
        fetchTestData();
    }, []);

    const fetchTestData = async () => {
        try {
            setLoading(true);
            // Replace with your actual API endpoint
            const response = await axios.get('https://api.example.com/test-data');
            setTestData(response.data);
            setLoading(false);
        } catch (err) {
            console.error('Error fetching test data:', err);
            // setError('Failed to load test data. Please try again later.');
            setLoading(false);

            // Fallback to dummy data for development
            setTestData({
                userName: 'Akash',
                subject: 'HISTORY',
                totalQuestions: 30,
                totalMarks: 30,
                timeLeft: '00:09:47',
                questions: [
                    { id: 1, text: 'What were the main causes of the Revolt of 1857 in India?' },
                    { id: 2, text: 'Describe the socio-economic and political conditions in India before the advent of British rule and how these factors contributed to the establishment of British dominance in the subcontinent. Discuss the role of key historical events such as the Battle of Plassey and the Battle of Buxar in consolidating British power.' },
                    { id: 3, text: 'What was the significance of the Regulating Act of 1773 in the administration of British India?' },
                    { id: 4, text: 'Analyze the impact of the Permanent Settlement on the agrarian structure of Bengal.' },
                    { id: 5, text: 'Discuss the role of social reformers like Raja Ram Mohan Roy and Ishwar Chandra Vidyasagar in 19th-century India.' },
                    { id: 6, text: 'Explain the causes and consequences of the First World War on India.' },
                    { id: 7, text: 'What were the key features of the Government of India Act, 1935?' },
                    { id: 8, text: 'Describe the Quit India Movement of 1942 and its significance in the freedom struggle.' }
                ]
            });
        }
    };

    const handleFileUpload = (e) => {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
            const newUploadedFiles = files.map(file => ({
                id: Math.random().toString(36).substring(2),
                url: URL.createObjectURL(file),
                file
            }));
            setUploadedFiles(prev => [...prev, ...newUploadedFiles]);
        }
    };

    const handleRemoveFile = (fileId) => {
        setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
    };

    const handleUploadClick = () => {
        document.getElementById('file-upload').click();
    };

    const handleSubmit = async () => {
        try {
            const formData = new FormData();
            uploadedFiles.forEach((fileObj, index) => {
                formData.append(`answer_file_${index}`, fileObj.file);
            });
            // const response = await axios.post('https://api.example.com/submit-test', formData, {
            //     headers: { 'Content-Type': 'multipart/form-data' }
            // });
            alert('Answers submitted successfully!');
            // navigate('/results');
        } catch (err) {
            console.error('Error submitting test:', err);
            alert('Failed to submit answers. Please try again.');
        }
    };

    if (loading) {
        return <div className="w-full min-h-screen bg-white flex justify-center items-center"><p className="text-xl font-medium text-[#3A1078]">Loading test data...</p></div>;
    }

    if (error) {
        return <div className="w-full min-h-screen bg-white flex justify-center items-center"><p className="text-xl font-medium text-red-500">{error}</p></div>;
    }

    return (
        <div className="w-full min-h-screen bg-white">
            <div className="w-full px-4 sm:px-6 md:px-[9%] py-4 md:py-6 bg-[#F9FDFF]">
                <div className="text-center mb-2">
                    <h1 className="text-4xl font-medium text-[#3A1078] mt-1 font-poppins">Topic: {testData.subject}</h1>
                    <p className="text-m text-[#c44b4b] mt-2 font-medium font-poppins">⚠ Note: Refreshing this page will erase your progress.</p>
                </div>
                <div className="flex justify-center text-center">
                    <p className="text-xl md:text-2xl lg:text-3xl font-medium text-[#3A1078] tracking-tighter md:mt-2 font-poppins">
                        {translator("Answer all questions with proper numbering, and submit your answers.", "सभी प्रश्नों को सही संख्या सहित उत्तर दें, और अपने उत्तर जमा करें।")}
                    </p>
                </div>

                {/* Test Container */}
                <div className="mt-4 md:mt-8">
                    <div
                        className={`bg-white/80 rounded-[20px] md:rounded-[38px] border border-black/80 shadow-lg flex ${isMobile ? 'flex-col' : 'flex-row'}`}
                        style={{ height: isMobile ? 'auto' : '85vh' }}
                    >

                        {/* Left Column (Questions and Info Bar) */}
                        <div className={`flex flex-col ${isMobile ? 'w-full' : 'w-2/3'}`}>
                            <div
                                className={`px-4 py-4 md:pl-[5%] md:py-6 md:pr-[8%] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent ${!isMobile ? 'flex-1' : ''}`}
                                style={{ height: isMobile ? '50vh' : undefined }}
                            >
                                {testData.questions.map((question, index) => (
                                    <div key={question.id} className="mb-6 md:mb-8">
                                        <p className="text-lg md:text-xl lg:text-2xl font-medium font-advent-pro">
                                            Question {index + 1} :<br />
                                            {question.text}
                                        </p>
                                    </div>
                                ))}
                            </div>

                            <div className={`bg-[#4C60A5] h-[60px] md:h-[87px] ${isMobile ? 'rounded-b-[20px]' : 'rounded-bl-[38px]'} px-4 md:px-[8%] flex justify-between items-center`}>
                                <p className="text-white text-sm md:text-lg lg:text-2xl font-normal uppercase font-jost">{translator("total questions: ", "कुल प्रश्न: ")} {testData.totalQuestions}</p>
                                <p className="text-white text-sm md:text-lg lg:text-2xl font-normal uppercase font-jost">{translator("total marks: ", "कुल अंक: ")} {testData.totalMarks}</p>
                            </div>
                        </div>

                        {/* Right Column (User Info and Actions) */}
                        <div className={`flex flex-col gap-1 justify-around md:gap-4 ${isMobile ? 'w-full p-4' : 'w-1/3 p-6'} border-t-2 md:border-t-0 md:border-l-2 border-gray-200`}>
                            <div>
                                <p className="text-[#3A1078] font-bold text-base md:text-xl font-jost">{translator("Name: ", "नाम: ")} {testData.userName}</p>
                            </div>

                            <div>
                                <p className="text-black font-bold text-base md:text-xl font-jost">{translator("Time Left", "समय बाकी")}</p>
                                <p className="text-[#08A064] font-bold text-2xl md:text-3xl font-jost">{testData.timeLeft}</p>
                            </div>

                            {/* --- CHANGED LINE --- */}
                            {/* Removed 'flex-grow' from this div to allow elements below to move up */}
                            <div></div>

                            {/* Uploaded Files Section */}
                            <div className="border border-dashed border-black/50 rounded-lg p-3 w-full">
                                <p className="text-[#8B8B8B] text-xs md:text-sm text-center mb-2 font-average-sans">{translator("Uploaded Files", "अपलोड किए गए फाइल")}</p>

                                <div className={`max-h-[70px] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent ${uploadedFiles.length === 0 ? 'hidden' : ''}`}>
                                    <div className="grid grid-cols-5 gap-2">
                                        {uploadedFiles.map((file, index) => (
                                            <div key={file.id} className="w-[39px] h-[39px] border border-[#959595] rounded-lg overflow-hidden relative group">
                                                <img src={file.url} alt={`Upload ${index}`} className="w-full h-full object-cover" />
                                                <button
                                                    className="absolute top-0 right-0 bg-red-500 text-white w-[18px] h-[18px] flex items-center justify-center rounded-bl-lg opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                                                    onClick={(e) => { e.stopPropagation(); handleRemoveFile(file.id); }}>
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
                                                </button>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                                {uploadedFiles.length === 0 ? (
                                    <p className="text-[#8B8B8B] text-xs text-center py-4">{translator("No files uploaded", "कोई फाइल अपलोड नहीं की गई")}</p>
                                ) : (
                                    <p className="text-[#8B8B8B] text-xs text-center mt-1">{uploadedFiles.length} {translator("file", "फाइल")}{uploadedFiles.length !== 1 ? translator("s", "") : ''} {translator("uploaded", "अपलोड की गई")}</p>
                                )}
                            </div>

                            <div className="bg-[#342499] rounded-[20px] p-4 shadow-md flex flex-col items-center cursor-pointer hover:bg-[#2b1e7e] transition-colors w-full" onClick={handleUploadClick}>
                                <div className="w-8 h-8 flex flex-col items-center justify-center">
                                    <img src="/images/upload-vector1.svg" alt="Upload" className="h-5" />
                                    <img src="/images/upload-vector2.svg" alt="Upload" className="w-7" />
                                </div>
                                <p className="text-[#EBB12B] font-bold text-sm mt-2 uppercase font-jost">{translator("upload files", "फाइल अपलोड करें")}</p>
                            </div>

                            <button className="bg-[#005FD0] text-white rounded-[12px] py-6 md:py-1 shadow-md font-medium text-lg md:text-xl lg:text-2xl font-afacad hover:bg-[#0050b1] transition-colors w-full" onClick={handleSubmit}>
                                {translator("Confirm and Submit", "सत्यापित और सबमिट करें")}
                            </button>
                        </div>
                    </div>
                </div>

                <input type="file" multiple onChange={handleFileUpload} className="hidden" id="file-upload" accept="image/*" />
            </div>
        </div>
    );
};

export default HandwrittenTest;