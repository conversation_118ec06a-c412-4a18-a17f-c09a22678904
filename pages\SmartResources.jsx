import React, { useState, useEffect } from 'react';
import { Search, Star, ChevronDown, LayoutDashboard, StickyNote, BookOpenText, CalendarDays, UserCog, BrainCircuit, PlusCircle, Home, BookText, ClipboardCheck, Users, X, Menu, Dock, Trash2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

// const userId = '6836b855cc511b5579be9a69';

// Translator function
const translator = (word1, word2) =>
  localStorage.getItem("lang") && localStorage.getItem("lang").toLowerCase().includes("english")
    ? word1
    : localStorage.getItem("lang")
      ? word2
      : word1;

const StarToggle = ({ resourceId, isStarredInitial, onStarChange }) => {
  const [isStarred, setIsStarred] = useState(isStarredInitial);

  const handleStarToggle = async (e) => {
    e.stopPropagation(); // Prevent card click when starring
    const newStarredStatus = !isStarred;
    setIsStarred(newStarredStatus);

    try {
      await axios.patch(`${import.meta.env.VITE_BACKEND_1_SERVER_URL}/planner/addToFavorites`, {
        wbStrId: resourceId,
        value: newStarredStatus
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // Update local storage, use effect, fuction log resources 
      const starredResources = JSON.parse(localStorage.getItem('starredResources') || '{}');
      starredResources[resourceId] = newStarredStatus;
      localStorage.setItem('starredResources', JSON.stringify(starredResources));

      // Notify parent component about the star change
      if (onStarChange) {
        onStarChange(resourceId, newStarredStatus);
      }
    } catch (error) {
      console.error('Error updating starred status:', error);
      setIsStarred(!newStarredStatus); // Revert if API call fails
    }
  };

  return (
    <button
      onClick={handleStarToggle}
      className="text-gray-400 hover:text-yellow-400 focus:outline-none"
      aria-label={isStarred ? translator("Unstar this resource", "इस संसाधन को अनस्टार करें") : translator("Star this resource", "इस संसाधन को स्टार करें")}
    >
      <Star size={18} fill={isStarred ? "#F59E0B" : "none"} stroke={isStarred ? "#F59E0B" : "currentColor"} />
    </button>
  );
};

const NavItem = ({ icon, label, active = false, textColorClass = '', onClick, showPopUp = false }) => (
  <li className="mb-4 rounded-lg relative">
    <a
      href="#"
      className={`
        relative flex items-center p-3 rounded-lg
        ${active ? 'border-b-2 border-indigo-400' : 'hover:bg-indigo-600 hover:text-white'}
        ${textColorClass || 'text-gray-600'}
      `}
      onClick={onClick}
    >
      {icon}
      <span className="ml-4 font-medium">{label}</span>

      {active && (
        <span className="absolute bottom-0 left-0 w-full h-1 bg-white rounded-b-lg"></span>
      )}
    </a>
  </li>
);

const Sidebar = ({ isOpen, toggleSidebar, activePage }) => (
  <>
    {/* Desktop Sidebar */}
    <div className="w-64 bg-white h-screen p-6 flex-col justify-between hidden lg:flex">
      <div>
        <div className="flex items-center mb-12">
          <div className="flex items-center">
            <img src="/logo.png" alt={translator("AI Classroom Logo", "एआई क्लासरूम लोगो")} className="h-8 w-8" />
            <span className="ml-2 text-xl font-semibold">{translator('AI Classroom', 'एआई क्लासरूम')}</span>
          </div>
        </div>
        <nav>
          <ul>
            <NavItem
              icon={<Home size={20} />}
              label={translator("Dashboard", "डैशबोर्ड")}
              textColorClass="text-black"
              active={activePage === 'dashboard'}
            />
            <NavItem
              icon={<BookText size={20} />}
              label={translator("Notes", "नोट्स")}
              textColorClass="text-black"
              active={activePage === 'notes'}
            />
            <NavItem
              icon={<ClipboardCheck size={20} />}
              label={translator("Exams", "परीक्षाएँ")}
              textColorClass="text-black"
              active={activePage === 'exams'}
            />
            <NavItem
              icon={<CalendarDays size={20} />}
              label={translator("Planner", "योजनाकार")}
              textColorClass="text-black"
              active={activePage === 'planner'}
            />
            <NavItem
              icon={<Users size={20} />}
              label={translator("Mentor & Progress", "मेंटर और प्रगति")}
              textColorClass="text-black"
              active={activePage === 'mentor'}
            />
            <NavItem
              icon={<Dock size={20} />}
              label={translator("Smart Resources", "स्मार्ट संसाधन")}
              textColorClass="text-black"
              active={activePage === 'resources'}
              showPopUp={activePage === 'resources'}
            />
          </ul>
        </nav>
      </div>
    </div>

    {/* Mobile Sidebar */}
    <div className={`fixed top-0 left-0 w-64 bg-white h-screen p-6 flex-col justify-between shadow-md z-50 transition-transform duration-300 ease-in-out lg:hidden ${isOpen ? 'translate-x-0' : '-translate-x-full'}`}>
      <div>
        <div className="flex items-center justify-between mb-12">
          <div className="flex items-center">
            <img src="/logo.png" alt={translator("AI Classroom Logo", "एआई क्लासरूम लोगो")} className="h-8 w-8" />
            <span className="ml-2 text-xl font-semibold">{translator('AI Classroom', 'एआई क्लासरूम')}</span>
          </div>
          <button onClick={toggleSidebar} className="text-gray-600 hover:text-gray-900">
            <X size={24} />
          </button>
        </div>
        <nav>
          <ul>
            <NavItem
              icon={<Home size={20} />}
              label={translator("Dashboard", "डैशबोर्ड")}
              textColorClass="text-black"
              onClick={toggleSidebar}
              active={activePage === 'dashboard'}
            />
            <NavItem
              icon={<BookText size={20} />}
              label={translator("Notes", "नोट्स")}
              textColorClass="text-black"
              onClick={toggleSidebar}
              active={activePage === 'notes'}
            />
            <NavItem
              icon={<ClipboardCheck size={20} />}
              label={translator("Exams", "परीक्षाएँ")}
              textColorClass="text-black"
              onClick={toggleSidebar}
              active={activePage === 'exams'}
            />
            <NavItem
              icon={<CalendarDays size={20} />}
              label={translator("Planner", "योजनाकार")}
              textColorClass="text-black"
              onClick={toggleSidebar}
              active={activePage === 'planner'}
            />
            <NavItem
              icon={<Users size={20} />}
              label={translator("Mentor & Progress", "मेंटर और प्रगति")}
              textColorClass="text-black"
              onClick={toggleSidebar}
              active={activePage === 'mentor'}
            />
            <NavItem
              icon={<Dock size={20} />}
              label={translator("Smart Resources", "स्मार्ट संसाधन")}
              textColorClass="text-black"
              onClick={toggleSidebar}
              active={activePage === 'resources'}
              showPopUp={activePage === 'resources'}
            />
          </ul>
        </nav>
      </div>
    </div>
  </>
);

const MobileFAB = ({ toggleSidebar }) => (
  <button
    onClick={toggleSidebar}
    className="fixed top-4 left-4 p-2 lg:hidden z-30 flex items-center justify-center text-purple-800 bg-white rounded-full shadow-lg hover:bg-gray-100"
    aria-label={translator("Open navigation", "नेविगेशन खोलें")}
  >
    <Menu size={28} />
  </button>
);

const ConfirmDeleteModal = ({ isOpen, onClose, onConfirm }) => {
  const [reason, setReason] = useState('');

  if (!isOpen) return null;

  const handleConfirm = () => {
    onConfirm(reason);
    setReason('');
  };

  const handleCancel = () => {
    onClose();
    setReason('');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
        <h2 className="text-xl font-bold text-red-600 mb-4 text-center">
          {translator('Confirm Delete ?', 'हटाने की पुष्टि करें ?')}
        </h2>
        <div className="mb-4">
          <label htmlFor="deleteReason" className="block text-sm font-medium text-gray-700 mb-1">
            {translator('Type your Reason', 'अपना कारण टाइप करें')}
          </label>
          <textarea
            id="deleteReason"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder={translator('Enter Your Reason (Optional)', 'अपना कारण दर्ज करें (वैकल्पिक)')}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm"
            rows="3"
          ></textarea>
        </div>
        <div className="flex justify-between mt-6">
          <button
            onClick={handleCancel}
            className="px-4 py-2 bg-blue-500 text-white text-sm font-medium rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {translator('Cancel', 'रद्द करें')}
          </button>
          <button
            onClick={handleConfirm}
            className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            {translator('Delete', 'हटाएं')}
          </button>
        </div>
      </div>
    </div>
  );
};

const ResourceCard = ({ id, title, description, status, date, completedCount, totalCount, isStarred, onOpenDeleteModal, onStarChange }) => {
  let statusBgColorClass = '';
  let statusTextColorClass = '';
  const navigate = useNavigate();

  switch (status.toLowerCase()) {
    case 'finished':
    case 'completed':
      statusBgColorClass = 'bg-green-100';
      statusTextColorClass = 'text-green-700';
      break;
    case 'pending':
    case 'incomplete':
      statusBgColorClass = 'bg-yellow-100';
      statusTextColorClass = 'text-yellow-700';
      break;
    case 'unscheduled':
      statusBgColorClass = 'bg-blue-100';
      statusTextColorClass = 'text-blue-700';
      break;
    default:
      statusBgColorClass = 'bg-gray-100';
      statusTextColorClass = 'text-gray-700';
  }

  return (
    <button onClick={() => {
      console.log("clicked");
      const dataForBreakdown = {
        "wbStrId": id,
        "shouldDoPostReq": false,
      };
      localStorage.setItem("dataForBreakdown", JSON.stringify(dataForBreakdown));
      localStorage.setItem("wbStrId2", id);
      console.log("wbStrId2 :", localStorage.getItem("wbStrId2"));
      navigate("/structured-breakdown");
    }}
      className="w-full text-left bg-white p-5 rounded-xl shadow-sm border border-gray-200 flex flex-col justify-between hover:shadow-lg focus:shadow-lg transition-shadow focus:outline-none">
      <div>
        <div className="flex justify-between items-start mb-3">
          <span className={`px-2.5 py-0.5 text-xs font-semibold rounded-full ${statusBgColorClass} ${statusTextColorClass}`}>
            {translator(status,
              status.toLowerCase() === 'finished' || status.toLowerCase() === 'completed' ? 'समाप्त' :
                status.toLowerCase() === 'pending' || status.toLowerCase() === 'incomplete' ? 'लंबित' :
                  status.toLowerCase() === 'unscheduled' ? 'अनियोजित' : status
            )}
          </span>
          <div className="flex items-center space-x-2">
            <button
              onClick={(e) => { e.stopPropagation(); onOpenDeleteModal(id); }}
              className="text-gray-400 hover:text-red-500 focus:outline-none"
              aria-label={translator("Delete this resource", "इस संसाधन को हटाएँ")}
            >
              <Trash2 size={18} />
            </button>
            <StarToggle resourceId={id} isStarredInitial={isStarred} onStarChange={onStarChange} />
          </div>
        </div>
        <h3 className="text-lg font-semibold text-gray-800 mb-1.5">{title}</h3>
        <p className="text-sm text-gray-500 leading-relaxed mb-4 line-clamp-3">{description}</p>
      </div>
      <div className="flex justify-between items-center text-xs text-gray-500 pt-2 mt-auto">
        <span className="flex items-center">
          <CalendarDays size={14} className="mr-1.5 text-gray-400" />
          {date}
        </span>
        {(completedCount !== undefined && totalCount !== undefined) && (
          <span>{`${completedCount}/${totalCount}`}</span>
        )}
      </div>
    </button>
  );
};

const MyResourcesPage = () => {
  const navigate = useNavigate();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(translator('All', 'सभी'));
  const [resources, setResources] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [resourceToDeleteId, setResourceToDeleteId] = useState(null);
  const [starredResources, setStarredResources] = useState(null);

  const tabs = [
    translator('All', 'सभी'),
    translator('Scheduled', 'निर्धारित'),
    translator('Unscheduled', 'अनियोजित'),
    translator('Starred', 'तारांकित')
  ];

  const fetchResources = async () => {
    setIsLoading(true);

    const userId = JSON.parse(localStorage.getItem("user-data"))?.userId;
    console.log("userId", userId);
    try {
      const response = await fetch(`${import.meta.env.VITE_BACKEND_1_SERVER_URL}/planner/getAllSchedulesFromUser/${userId}`);

      const data = await response.json();
      console.log('Fetched resources:', data);

      if (data.success && Array.isArray(data.data)) {
        const starredResources = JSON.parse(localStorage.getItem('starredResources') || '{}');


        const transformedResources = data.data.map((item, index) => {
          const totalTopics = item.totalSubTopics || (Math.floor(Math.random() * 10) + 15);
          const completedTopics = item.subTopicsCompleted || Math.floor(Math.random() * (totalTopics + 1));

          return {
            id: item._id || index.toString(),
            title: item.scheduleTitle || translator('Untitled Resource', 'अनाम संसाधन'),
            description: item.scheduleDescription || translator('No description available', 'कोई विवरण उपलब्ध नहीं'),
            status: item.completedAll ? translator('Completed', 'समाप्त') : translator('Incomplete', 'लंबित'),
            date: item.createdAt ? new Date(item.createdAt).toLocaleDateString('en-GB') : translator('No date', 'कोई तिथि नहीं'),
            completedCount: completedTopics,
            totalCount: totalTopics,
            isStarred: starredResources[item._id] || false
          };
        });
        console.log('Transformed resources:', transformedResources);

        setResources(transformedResources);
      } else {
        setError(translator('Failed to fetch resources', 'संसाधन प्राप्त करने में विफल') + (data.message ? `: ${data.message}` : ''));
      }
    } catch (err) {
      setError(translator('Error fetching resources', 'संसाधन प्राप्त करने में त्रुटि') + `: ${err.message}`);
      console.error('Error fetching resources:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchResources();
  }, []);

  // Handle star change callback
  const handleStarChange = (resourceId, newStarredStatus) => {
    setResources(prevResources =>
      prevResources.map(resource =>
        resource.id === resourceId
          ? { ...resource, isStarred: newStarredStatus }
          : resource
      )
    );
  };

  const handleOpenDeleteModal = (idToDelete) => {
    setResourceToDeleteId(idToDelete);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setResourceToDeleteId(null);
  };

  const handleConfirmDelete = async (reason) => {
    if (resourceToDeleteId) {
      try {
        await axios.delete(`${import.meta.env.VITE_BACKEND_1_SERVER_URL}/planner/deleteSchedule/${resourceToDeleteId}`);

        const starredResources = JSON.parse(localStorage.getItem('starredResources') || '{}');
        if (starredResources[resourceToDeleteId]) {
          delete starredResources[resourceToDeleteId];
          localStorage.setItem('starredResources', JSON.stringify(starredResources));
        }

        setResources(prevResources => prevResources.filter(resource => resource.id !== resourceToDeleteId));
        console.log(`Deleted resource with ID: ${resourceToDeleteId}, Reason: ${reason}`);
      } catch (error) {
        console.error('Error deleting resource:', error);
      } finally {
        handleCloseDeleteModal();
      }
    }
  };

  const filteredResources = resources.filter(resource => {
    let matchesTab = true;
    const translatedStarred = translator('Starred', 'तारांकित');
    const translatedScheduled = translator('Scheduled', 'निर्धारित');
    const translatedUnscheduled = translator('Unscheduled', 'अनियोजित');
    const translatedStatusIncomplete = translator('Incomplete', 'लंबित');
    const translatedStatusUnscheduled = translator('Unscheduled', 'अनियोजित');

    if (activeTab === translatedStarred) {
      matchesTab = resource.isStarred;
    } else if (activeTab === translatedScheduled) {
      matchesTab = resource.status === translatedStatusIncomplete || resource.status === 'Incomplete';
    } else if (activeTab === translatedUnscheduled) {
      matchesTab = resource.status === translatedStatusUnscheduled || resource.status === 'Unscheduled';
    }

    const matchesSearch =
      resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesTab && matchesSearch;
  });

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  if (isLoading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} activePage="resources" />
        <MobileFAB toggleSidebar={toggleSidebar} />
        <main className="flex-1 px-4 pb-4 pt-20 lg:pt-8 lg:px-8 overflow-y-auto flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">{translator('Loading resources...', 'संसाधन लोड हो रहे हैं...')}</p>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-gray-50">
        <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} activePage="resources" />
        <MobileFAB toggleSidebar={toggleSidebar} />
        <main className="flex-1 px-4 pb-4 pt-20 lg:pt-8 lg:px-8 overflow-y-auto flex items-center justify-center">
          <div className="text-center">
            <StickyNote size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600 text-lg font-medium">{error}</p>
            <button
              onClick={() => { setIsLoading(true); setError(null); fetchResources(); }}
              className="mt-4 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              {translator('Retry', 'पुनः प्रयास करें')}
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} activePage="resources" />
      <MobileFAB toggleSidebar={toggleSidebar} />
      <main className="flex-1 px-4 pb-4 pt-20 lg:pt-8 lg:px-8 overflow-y-auto">
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 sm:mb-8">
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-4 sm:mb-0">{translator('My Resources', 'मेरे संसाधन')}</h2>
          <button className="bg-[#2563EB] hover:bg-blue-700 text-white font-semibold py-2 px-4 sm:py-2.5 sm:px-5 rounded-lg flex items-center space-x-2 transition-colors self-start sm:self-auto"
            onClick={() => { navigate('/ai-resource-finder'); }}
          >
            <PlusCircle size={20} />
            <span>{translator('Create New', 'नया बनाएँ')}</span>
          </button>
        </header>

        <div className="mb-8 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-y-6 lg:gap-y-0">
          <div className="flex space-x-1 p-1 rounded-lg overflow-x-auto w-full lg:w-auto">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-3 py-1.5 sm:px-4 text-sm font-medium rounded-md transition-all duration-200 ease-in-out flex-shrink-0 whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
                  ${activeTab === tab
                    ? 'text-white shadow-sm'
                    : 'text-gray-600 hover:bg-white hover:shadow-sm'
                  }`}
                style={activeTab === tab ? { background: 'linear-gradient(90deg, #6F83FF 0%, #7356B2 100%)' } : {}}
              >
                {tab === translator('Starred', 'तारांकित') ? <Star size={14} className="inline mr-1.5 mb-0.5" /> : null}
                {tab}
              </button>
            ))}
          </div>

          <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:space-x-4 w-full lg:w-auto">
            <div className="relative w-full sm:flex-1 lg:w-64">
              <input
                type="text"
                placeholder={translator("Search resources...", "संसाधनों में खोजें...")}
                className="pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-full text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-shadow shadow-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Search size={18} className="absolute left-3.5 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
            <div className="relative w-full sm:w-auto">
              <button className="flex w-full sm:w-auto justify-center sm:justify-start items-center space-x-2 text-sm text-gray-700 font-medium border border-gray-300 px-4 py-2.5 rounded-full hover:bg-gray-100 transition-colors shadow-sm bg-white">
                <span>{translator('Sort By', 'इसके अनुसार क्रमबद्ध करें')}</span>
                <ChevronDown size={16} />
              </button>
            </div>
          </div>
        </div>

        {filteredResources.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6">
            {filteredResources.map((resource) => (
              <ResourceCard
                key={resource.id}
                id={resource.id}
                title={resource.title}
                description={resource.description}
                status={resource.status}
                date={resource.date}
                completedCount={resource.completedCount}
                totalCount={resource.totalCount}
                isStarred={resource.isStarred}
                onOpenDeleteModal={handleOpenDeleteModal}
                onStarChange={handleStarChange}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <StickyNote size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600 text-lg font-medium">{translator(`No resources found for "${activeTab}".`, `"${activeTab}" के लिए कोई संसाधन नहीं मिला।`)}</p>
            <p className="text-gray-500 text-sm">{translator('Try selecting a different tab or creating a new resource.', 'कोई भिन्न टैब चुनने या नया संसाधन बनाने का प्रयास करें।')}</p>
          </div>
        )}
      </main>
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
      />
    </div>
  );
};

export default MyResourcesPage;