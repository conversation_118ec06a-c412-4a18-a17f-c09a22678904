/* Global Styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: #f9fafb;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

/* Container Layout */
.container1 {
    display: flex;
    min-height: 100vh;
    width: 100%;
    max-width: 1200px;
    margin: 20px auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    overflow: hidden;
    background-color: #fff;
}

.form-container {
    flex: 1;
    background-color: #fff;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.info-container {
    width: 40%;
    background-color: #f0f4ff;
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    text-align: center;
}

/* Form Styles */
.form-content {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.form-title {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.form-subtitle {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="tel"] {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    box-sizing: border-box;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.country-code-select:focus {
    outline: none;
    border-color: #7c4dff;
    box-shadow: 0 0 0 2px rgba(124, 77, 255, 0.2);
}

/* Mobile Input Container */
.mobile-input-container {
    display: flex;
    width: 100%;
}

.country-code-select {
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-right: none;
    border-top-left-radius: 8px;
    /* Corrected to match the image */
    border-bottom-left-radius: 8px;
    /* Corrected to match the image */
    font-size: 1rem;
    background-color: #fff;
    height: 47px;
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%236B7280%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-13%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2013l128%20127.9c3.6%203.6%207.8%205.4%2013%205.4s9.4-1.8%2013-5.4L287%2095.2c3.5-3.5%205.4-7.8%205.4-13%200-5-1.9-9.4-5.4-13z%22%2F%3E%3C%2Fsvg%3E');
    background-repeat: no-repeat;
    background-position: right 20em top 70%;
    background-size: 0.65em auto;
    padding-right: 2.5em;
    min-width: 120px;
    display: flex;
    /* Added to center text vertically */
    align-items: center;
    /* Added to center text vertically */
}

.mobile-number-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-top-right-radius: 8px;
    /* Corrected to match the image */
    border-bottom-right-radius: 8px;
    /* Corrected to match the image */
    height: 48px;
    font-size: 1rem;
    /* Placeholder text styling */
    color: #6B7280;
    /* Grey color for placeholder */
}

/* Error Styles */
.input-error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 1px #ef4444;
}

.error-message {
    display: flex;
    align-items: center;
    color: #ef4444;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.error-message svg {
    margin-right: 0.5rem;
    flex-shrink: 0;
}

/* Checkbox Styles */
.form-checkbox {
    display: flex;
    align-items: center;
    margin: 20px 0 30px;
}

.form-checkbox input {
    margin-right: 10px;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.form-checkbox span {
    font-size: 14px;
    color: #555;
}

.link {
    color: #7c4dff;
    text-decoration: none;
    font-weight: 500;
}

.link:hover {
    text-decoration: underline;
}

/* Button Styles */
.btn-primary {
    width: 25%;
    padding: 14px;
    background-color: #7c4dff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-primary:hover {
    background-color: #6a3de8;
}

.btn-secondary {
    padding: 12px 30px;
    background-color: transparent;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-secondary:hover {
    background-color: #f5f5f5;
}

.button-group {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: 40px;

}


.mt-2 {
    margin-top: 1rem;
}

/* OTP Section Styles */
.otp-section {
    text-align: center;
    margin-top: 40px;
}

.otp-section h3 {
    font-size: 18px;
    color: #333;
    margin-bottom: 25px;
}

.otp-inputs {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin: 20px 0 30px;
    flex-wrap: wrap;
}

.otp-box {
    width: 60px;
    height: 60px;
    font-size: 28px;
    text-align: center;
    border: 2px solid #ccc;
    border-radius: 10px;
    outline: none;
    transition: all 0.2s ease-in-out;
    background-color: #fff;
    box-sizing: border-box;
}

.otp-box:focus {
    border-color: #7c4dff;
    box-shadow: 0 0 8px rgba(124, 77, 255, 0.25);
}

/* Student Type Cards */
.student-type-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 30px;
}

.student-type-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s;
}

.student-type-card:hover {
    border-color: #7c4dff;
    box-shadow: 0 4px 12px rgba(124, 77, 255, 0.1);
}



/* Info Container (Right Side) Styles */
.logo-container {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo img {
    height: 30px;
    width: 30px;
}

.logo span {
    font-size: 22px;
    font-weight: 700;
    color: #333;
}

.illustration {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.illustration img {
    max-width: 90%;
    height: auto;
}

.welcome-text {
    text-align: center;
    margin-top: 20px;
}

.welcome-text h2 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.welcome-text p {
    font-size: 15px;
    color: #666;
    line-height: 1.5;
    max-width: 300px;
    margin: 0 auto;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .container1 {
        flex-direction: column;
        min-height: auto;
        margin: 0;
        border-radius: 0;
    }

    .info-container {
        width: 100%;
        order: -1;
        padding: 30px;
        height: auto;
        min-height: 350px;
        justify-content: center;
    }

    .illustration img {
        max-width: 60%;
    }

    .form-container {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .form-title {
        font-size: 24px;
    }

    .form-subtitle {
        font-size: 15px;
    }

    .otp-box {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }

    .btn-primary {
        width: 100%;
    }

    .info-container {
        padding: 20px;
        min-height: 300px;
    }

    .illustration img {
        max-width: 50%;
    }
}

@media (max-width: 480px) {

    .form-container,
    .info-container {
        padding: 20px;
    }

    .otp-inputs {
        gap: 10px;
    }

    .otp-box {
        width: 45px;
        height: 45px;
        font-size: 20px;
    }

    .form-title {
        font-size: 22px;
    }

    .form-subtitle {
        font-size: 14px;
    }

    .form-checkbox span {
        font-size: 13px;
    }

    .country-code-select {
        min-width: 100px;
        padding-right: 1.5em;
    }
}