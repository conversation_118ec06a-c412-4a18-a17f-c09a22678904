import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { UploadCloud, FileEdit, Plus, Minus, ChevronDown, Clock } from "lucide-react";

const useUploadPdf = () => {
  const [fileUrl, setFileUrl] = useState(null);

  const uploadPdf = async (file) => {
    const formData = new FormData();
    formData.append('pdf', file);

    try {
      // Using a proxy server or direct backend URL that supports CORS
      const response = await fetch("https://api.aiclassroom.in/pdfUpload/upload", {
        method: 'POST',
        body: formData,
        headers: {
          // Add any required headers here
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      setFileUrl(data.fileUrl);
      return data.fileUrl;
    } catch (error) {
      console.error('PDF upload failed:', error);
      // Fallback to blob URL for demo purposes
      const blobUrl = URL.createObjectURL(file);
      setFileUrl(blobUrl);
      return blobUrl;
    }
  };

  return { uploadPdf, fileUrl };
};

export default function ExamCreationPage() {
  const [examType, setExamType] = useState('mcq');
  const [activeTab, setActiveTab] = useState('upload');
  const [displayTime, setDisplayTime] = useState('');
  const [timeOption, setTimeOption] = useState('preset');
  const [customTimeInput, setCustomTimeInput] = useState('');
  const [questionsOption, setQuestionsOption] = useState('preset');
  const [numQuestions, setNumQuestions] = useState(10);
  const [customQuestions, setCustomQuestions] = useState('');
  const [difficulty, setDifficulty] = useState('medium');
  const [pdfFile, setPdfFile] = useState(null);
  const [topicText, setTopicText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [imageslist, setImageslist] = useState(null);

  const fileInputRef = useRef(null);
  const { uploadPdf, fileUrl } = useUploadPdf();
  const randomId = Math.random().toString(36).substr(2, 5);
  const nav = useNavigate();

  const handleExamTypeChange = (type) => {
    setExamType(type);
  };

  const handleIncrementQuestions = () => {
    if (questionsOption === 'preset') {
      setNumQuestions(prev => prev + 1);
    }
  };

  const handleDecrementQuestions = () => {
    if (questionsOption === 'preset') {
      setNumQuestions(prev => (prev > 1 ? prev - 1 : 1));
    }
  };

  const handleFileBrowse = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event) => {
    const file = event.target.files[0];
    if (file && file.type === "application/pdf") {
      try {
        setIsLoading(true);
        await uploadPdf(file);
        setPdfFile(file);
      } catch (error) {
        alert("Failed to upload PDF. Using fallback method.");
        const blobUrl = URL.createObjectURL(file);
        setPdfFile(file);
        setFileUrl(blobUrl);
      } finally {
        setIsLoading(false);
      }
    } else {
      alert("Please upload a valid PDF file.");
    }
  };

  const handleTopicChange = (e) => {
    setTopicText(e.target.value);
  };

  useEffect(() => {
    const findData = async () => {
      if (!fileUrl) return;

      try {
        setIsLoading(true);
        const res = await fetch("https://py.aiclassroom.in/process", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",

          },
          body: JSON.stringify({
            file_uid: randomId,
            file_url: fileUrl,
          }),
        });

        if (res.ok) {
          const json = await res.json();
          setImageslist(json.Pdf_Pages_Data);
        } else {
          console.error('Processing failed with status:', res.status);
        }
      } catch (error) {
        console.error('PDF processing failed:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (fileUrl && pdfFile) {
      findData();
    }
  }, [fileUrl, pdfFile]);

  const performOCR = async (imagesList) => {
    try {
      const payload = {
        file_url_list: imagesList.map((x) => {
          const url = x.split("/").pop();
          return `files/pdf_pages/${url}`;
        })
      };

      const response = await fetch('https://py.aiclassroom.in/imgs2ocr/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',

        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      console.log(data)
      console.log(data)
      return data["OCR DATA"];
    } catch (error) {
      console.error('OCR failed:', error);
      return null;
    }
  };

  const generateQuestionsFromText = async (text, count) => {
    try {
      // Split text into chunks
      const words = text.split(/\s+/);
      const wordsPerChunk = Math.ceil(words.length / count);
      const chunks = [];

      for (let i = 0; i < words.length; i += wordsPerChunk) {
        chunks.push(words.slice(i, i + wordsPerChunk).join(' '));
      }

      // Generate questions for each chunk
      const questions = [];
      for (let i = 0; i < chunks.length && i < count; i++) {
        const chunk = chunks[i];
        try {
          const response = await fetch("https://api.abiv.in/prompt", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              message: chunk,
              prompt: `Return output strictly as raw JSON ONLY — do NOT include markdown syntax (like \`\`\`json), titles, or explanations.
Generate a multiple-choice question suitable for the "${difficulty}" level based on this content.
Format:
{
  "Question": "Your full question here",
  "options": ["Option A", "Option B", "Option C", "Option D"],
  "correct": "a" // indicating correct option (a, b, c, or d)
}`
            }),
          });

          const data = await response.json();
          const parsed = JSON.parse(data.output_text);
          questions.push({
            ...parsed,
            qNo: questions.length + 1,
            correctAnswer: ['a', 'b', 'c', 'd'].indexOf(parsed.correct)
          });
        } catch (err) {
          console.error('Question generation error for chunk:', err);
        }
      }

      return questions;
    } catch (error) {
      console.error('Question generation failed:', error);
      throw error;
    }
  };

  // Fixed time parsing function
  const parseTimeToSeconds = (timeString, isFromDropdown = false) => {
    if (!timeString) return 0;

    // If it's from dropdown (just minutes), convert directly
    if (isFromDropdown) {
      const minutes = parseInt(timeString);
      return minutes * 60;
    }

    // Handle format like "01 : 30 : 00"
    if (timeString.includes(':')) {
      const parts = timeString.split(':').map(part => part.trim());
      if (parts.length === 3) {
        const hours = parseInt(parts[0]) || 0;
        const minutes = parseInt(parts[1]) || 0;
        const seconds = parseInt(parts[2]) || 0;
        return (hours * 3600) + (minutes * 60) + seconds;
      }
    }

    // Handle custom input like "30m", "1h 30m", etc.
    const hourMatch = timeString.match(/(\d+)h/);
    const minuteMatch = timeString.match(/(\d+)m/);
    const secondMatch = timeString.match(/(\d+)s/);

    const hours = hourMatch ? parseInt(hourMatch[1]) : 0;
    const minutes = minuteMatch ? parseInt(minuteMatch[1]) : 0;
    const seconds = secondMatch ? parseInt(secondMatch[1]) : 0;

    return (hours * 3600) + (minutes * 60) + seconds;
  };

  const handleStartExam = async () => {
    // Validation
    if (!difficulty) {
      alert("Please select a difficulty level");
      return;
    }

    if ((activeTab === 'upload' && !pdfFile) || (activeTab === 'topic' && !topicText.trim())) {
      alert(`Please ${activeTab === 'upload' ? 'upload a PDF file' : 'enter a topic'}`);
      return;
    }

    // Get final question count
    let finalQuestionCount = 10; // default

    if (questionsOption === 'custom') {
      finalQuestionCount = parseInt(customQuestions) || 10;
    } else if (questionsOption === 'preset') {
      finalQuestionCount = numQuestions;
    } else if (!isNaN(parseInt(questionsOption))) {
      // It's a preset number from dropdown
      finalQuestionCount = parseInt(questionsOption);
    }

    if (finalQuestionCount <= 0) {
      alert("Please enter a valid number of questions");
      return;
    }

    // Get final time in seconds
    let totalSeconds = 0;

    if (timeOption === 'custom') {
      totalSeconds = parseTimeToSeconds(customTimeInput);
    } else if (timeOption === 'preset' && displayTime) {
      totalSeconds = parseTimeToSeconds(displayTime);
    } else if (!isNaN(parseInt(timeOption))) {
      // It's a preset time from dropdown
      totalSeconds = parseTimeToSeconds(timeOption, true);
    }

    // Default to 30 minutes if no valid time is set
    if (totalSeconds <= 0) {
      totalSeconds = 30 * 60; // 30 minutes default
    }

    console.log('Final settings:', {
      questionCount: finalQuestionCount,
      timeInSeconds: totalSeconds,
      timeInMinutes: totalSeconds / 60
    });

    setIsLoading(true);

    try {
      let questions = [];
      let topicName = '';

      if (activeTab === 'upload' && pdfFile) {
        // Process PDF flow
        let ocrData = [];

        if (imageslist) {
          ocrData = await performOCR(imageslist) || [];
        }
        console.log(ocrData)
        console.log(ocrData)
        if (ocrData.length > 0) {
          questions = await generateQuestionsFromText(ocrData + ' ', finalQuestionCount);
        } else {
          // Fallback - generate from PDF name if OCR fails
          questions = await generateQuestionsFromText(pdfFile.name.replace('.pdf', ''), finalQuestionCount);
        }
        topicName = pdfFile.name.replace('.pdf', '');
      } else if (activeTab === 'topic' && topicText.trim()) {
        // Generate questions directly from topic text
        questions = await generateQuestionsFromText(topicText, finalQuestionCount);
        topicName = topicText.substring(0, 50) + (topicText.length > 50 ? '...' : '');
      }

      if (questions.length === 0) {
        alert("Failed to generate questions. Please try again.");
        return;
      }

      console.log('Generated questions:', questions.length);
      console.log('Navigating with state:', {
        questions: questions.length,
        type: "mcq",
        time: totalSeconds,
        topic: topicName,
        totalQuestions: questions.length
      });

      // Navigate to MCQ exam with questions
      nav("/mcq-exam-quiz", {
        state: {
          questions,
          type: "mcq",
          time: totalSeconds,
          topic: topicName,
          totalQuestions: questions.length
        },
      });
    } catch (err) {
      alert("Something went wrong during exam generation.");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTimeDropdownChange = (e) => {
    const selectedValue = e.target.value;
    setTimeOption(selectedValue);

    if (selectedValue === 'custom') {
      setDisplayTime('');
      setCustomTimeInput('');
    } else if (selectedValue !== 'preset' && selectedValue !== "") {
      const minutes = parseInt(selectedValue);
      if (!isNaN(minutes)) {
        const h = Math.floor(minutes / 60);
        const m = minutes % 60;
        setDisplayTime(`${String(h).padStart(2, '0')} : ${String(m).padStart(2, '0')} : 00`);
      } else {
        setDisplayTime('');
      }
      setCustomTimeInput('');
    } else {
      if (selectedValue === "preset") setDisplayTime('');
    }
  };

  const handleDisplayTimeChange = (e) => {
    setDisplayTime(e.target.value);
    if (timeOption !== 'preset') {
      setTimeOption('preset');
    }
  };

  // Fixed questions dropdown handler
  const handleQuestionsDropdownChange = (e) => {
    const selectedVal = e.target.value;
    setQuestionsOption(selectedVal);

    if (selectedVal === 'custom') {
      setCustomQuestions('');
      setNumQuestions(0);
    } else if (selectedVal === 'preset') {
      setNumQuestions(10);
      setCustomQuestions('');
    } else if (!isNaN(parseInt(selectedVal))) {
      // It's a preset number
      setNumQuestions(parseInt(selectedVal));
      setCustomQuestions('');
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center py-10 px-4">
      <div className="w-full max-w-4xl">
        <div className="flex items-center mb-8">
          <button onClick={() => nav(-1)} className="text-gray-600 hover:text-gray-800">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-undo2-icon lucide-undo-2"><path d="M9 14 4 9l5-5" /><path d="M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11" /></svg>
          </button>
          <h1 className="text-3xl font-bold text-blue-800 ml-4">Create Exam</h1>
        </div>
        <p className="text-gray-600 mb-8">Enter Topic or Upload PDF and Select your preference</p>

        <div
          className="rounded-lg p-1 flex mb-8 w-full"
          style={{ background: "linear-gradient(90deg, #2164D7 0%, #AA3AEB 100%)" }}>
          <button
            className={`flex-1 py-2 px-4 text-center rounded-md font-semibold transition-colors duration-300 ease-in-out
              ${examType === 'mcq' ? 'bg-white text-purple-700 shadow-md' : 'bg-transparent text-white hover:bg-purple-500 hover:bg-opacity-30'}`}
            onClick={() => handleExamTypeChange('mcq')}
          >
            MCQ Exam
          </button>
          <button
            className={`flex-1 py-2 px-4 text-center rounded-md font-semibold transition-colors duration-300 ease-in-out
              ${examType === 'handwritten' ? 'bg-white text-purple-700 shadow-md' : 'bg-transparent text-white hover:bg-purple-500 hover:bg-opacity-30'}`}
            onClick={() => handleExamTypeChange('handwritten')}
          >
            Handwritten Exam
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left Side: Upload/Enter Topic */}
          <div className="bg-white p-8 rounded-lg shadow-lg">
            <div className="flex border-b mb-6">
              <button
                className={`py-3 px-4 font-semibold ${activeTab === 'upload' ? 'text-purple-700 border-b-2 border-purple-700' : 'text-gray-500 hover:text-purple-700 hover:border-b-2 hover:border-purple-700'}`}
                onClick={() => setActiveTab('upload')}
              >
                Upload PDF
              </button>
              <button
                className={`py-3 px-4 font-semibold ${activeTab === 'topic' ? 'text-purple-700 border-b-2 border-purple-700' : 'text-gray-500 hover:text-purple-700 hover:border-b-2 hover:border-purple-700'}`}
                onClick={() => setActiveTab('topic')}
              >
                Enter Topic
              </button>
            </div>

            {activeTab === 'upload' && (
              <div className="border-2 border-dashed border-blue-300 rounded-lg p-10 flex flex-col items-center justify-center text-center h-64">
                <UploadCloud className="w-16 h-16 text-gray-400 mb-4" />
                <p className="text-gray-600 mb-2">Drag & drop your syllabus file here</p>
                <p className="text-gray-500 text-sm mb-4">or</p>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  accept=".pdf"
                  className="hidden"
                />
                <button
                  onClick={handleFileBrowse}
                  className="text-white font-semibold py-3 px-6 rounded-lg hover:opacity-90 transition-colors"
                  style={{ background: "linear-gradient(178.36deg, #4387FF -31.43%, #604AD8 98.61%)" }}
                >
                  Browse Files
                </button>
                <p className="text-xs text-gray-400 mt-2">Supported Only : PDF</p>
                {pdfFile && (
                  <p className="text-sm text-green-600 mt-2">{pdfFile.name} uploaded</p>
                )}
                {isLoading && <p className="text-sm text-blue-600 mt-2">Processing PDF...</p>}
              </div>
            )}

            {activeTab === 'topic' && (
              <div className="border-2 border-dashed border-blue-300 rounded-lg p-6 flex flex-col items-center justify-center h-64">
                <textarea
                  placeholder="Enter your topic details here..."
                  className="w-full h-full p-4 text-gray-700 bg-transparent focus:outline-none resize-none placeholder-gray-500 text-sm"
                  value={topicText}
                  onChange={handleTopicChange}
                />
              </div>
            )}
          </div>

          {/* Right Side: Settings */}
          <div className="space-y-8">
            {/* Choose Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Choose Time</label>
              <div className="flex items-center space-x-2">
                <div className="relative w-5/12">
                  <input
                    type="text"
                    placeholder="HH : MM : SS"
                    value={displayTime}
                    onChange={handleDisplayTimeChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500 disabled:bg-gray-50"
                    disabled={timeOption === 'custom'}
                  />
                  <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                </div>
                <span className="text-gray-500">OR</span>
                <div className="relative w-7/12">
                  <select
                    className="appearance-none w-full bg-white border border-gray-300 text-gray-700 py-3 px-4 pr-8 rounded-lg leading-tight focus:outline-none focus:bg-white focus:border-purple-500"
                    value={timeOption}
                    onChange={handleTimeDropdownChange}
                  >
                    <option value="preset" disabled hidden>Select Custom</option>
                    <option value="15">15 Minutes</option>
                    <option value="30">30 Minutes</option>
                    <option value="45">45 Minutes</option>
                    <option value="60">1 Hour</option>
                    <option value="custom">Custom Time</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <ChevronDown className="w-4 h-4" />
                  </div>
                </div>
              </div>
              {timeOption === 'custom' && (
                <input
                  type="text"
                  placeholder="Enter custom time (e.g., 20m, 1h 30m)"
                  className="mt-2 w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
                  value={customTimeInput}
                  onChange={(e) => setCustomTimeInput(e.target.value)}
                />
              )}
            </div>

            {/* Select No of Questions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Select No. of Questions</label>
              <div className="flex items-center space-x-2">
                <div className="flex items-center border border-gray-300 rounded-lg w-5/12">
                  <button
                    onClick={handleDecrementQuestions}
                    className="p-3 text-purple-600 hover:bg-gray-100 rounded-l-lg disabled:opacity-50 disabled:bg-gray-50"
                    disabled={questionsOption !== 'preset'}
                  >
                    <Minus className="w-5 h-5" />
                  </button>
                  <input
                    type="number"
                    value={questionsOption === 'custom' ? '' : numQuestions}
                    readOnly={questionsOption !== 'preset'}
                    onChange={(e) => {
                      if (questionsOption === 'preset') {
                        setNumQuestions(parseInt(e.target.value) || 1);
                      }
                    }}
                    className="w-16 text-center border-l border-r border-gray-300 py-3 focus:outline-none disabled:bg-gray-50"
                    disabled={questionsOption !== 'preset'}
                  />
                  <button
                    onClick={handleIncrementQuestions}
                    className="p-3 text-purple-600 hover:bg-gray-100 rounded-r-lg disabled:opacity-50 disabled:bg-gray-50"
                    disabled={questionsOption !== 'preset'}
                  >
                    <Plus className="w-5 h-5" />
                  </button>
                </div>
                <span className="text-gray-500">OR</span>
                <div className="relative w-7/12">
                  <select
                    className="appearance-none w-full bg-white border border-gray-300 text-gray-700 py-3 px-4 pr-8 rounded-lg leading-tight focus:outline-none focus:bg-white focus:border-purple-500"
                    value={questionsOption}
                    onChange={handleQuestionsDropdownChange}
                  >
                    <option value="preset" disabled hidden>Select Custom</option>
                    <option value="5">5 Questions</option>
                    <option value="10">10 Questions</option>
                    <option value="15">15 Questions</option>
                    <option value="20">20 Questions</option>
                    <option value="custom">Custom Number</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <ChevronDown className="w-4 h-4" />
                  </div>
                </div>
              </div>
              {questionsOption === 'custom' && (
                <input
                  type="number"
                  placeholder="Enter custom number"
                  className="mt-2 w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
                  value={customQuestions}
                  onChange={(e) => setCustomQuestions(e.target.value)}
                />
              )}
            </div>

            {/* Select Difficulty Level */}
            <div>
              <label htmlFor="difficulty" className="block text-sm font-medium text-gray-700 mb-2">Select Difficulty Level</label>
              <div className="relative">
                <select
                  id="difficulty"
                  name="difficulty"
                  className="appearance-none w-full bg-white border border-gray-300 text-gray-700 py-3 px-4 pr-8 rounded-lg leading-tight focus:outline-none focus:bg-white focus:border-purple-500"
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value)}
                >
                  <option value="" disabled hidden>Select Difficulty</option>
                  <option value="easy">Easy</option>
                  <option value="medium">Medium</option>
                  <option value="hard">Hard</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <ChevronDown className="w-4 h-4" />
                </div>
              </div>
            </div>

            <button
              onClick={handleStartExam}
              disabled={isLoading}
              className={`w-full bg-purple-600 text-white font-semibold py-4 px-6 rounded-lg hover:bg-purple-700 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {isLoading ? 'Generating Exam...' : 'Start Exam'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}