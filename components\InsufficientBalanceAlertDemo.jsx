import React, { useState } from 'react';
import InsufficientBalanceAlert from './InsufficientBalanceAlert';

const InsufficientBalanceAlertDemo = () => {
  const [isAlertOpen, setIsAlertOpen] = useState(false);

  const handleShowAlert = () => {
    setIsAlertOpen(true);
  };

  const handleCloseAlert = () => {
    setIsAlertOpen(false);
  };

  const handleBuyPlan = () => {
    console.log('Redirecting to buy plan page...');
    // Add your buy plan logic here
    // For example: navigate('/pricing') or window.open('pricing-url')
  };

  return (
    <div className="p-8 min-h-screen bg-gray-100 flex flex-col items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
        <h1 className="text-2xl font-bold mb-4 text-gray-800">
          Insufficient Balance Alert Demo
        </h1>
        <p className="text-gray-600 mb-6">
          Click the button below to see the insufficient balance alert in action.
        </p>
        
        <button
          onClick={handleShowAlert}
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
        >
          Show Insufficient Balance Alert
        </button>

        <div className="mt-8 text-left">
          <h3 className="font-semibold text-gray-800 mb-2">Usage Example:</h3>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
{`import InsufficientBalanceAlert from './InsufficientBalanceAlert';

const [showAlert, setShowAlert] = useState(false);

// Show alert when user has insufficient balance
const handleAction = () => {
  if (userBalance < requiredAmount) {
    setShowAlert(true);
  } else {
    // Proceed with action
  }
};

<InsufficientBalanceAlert
  isOpen={showAlert}
  onClose={() => setShowAlert(false)}
  onBuyPlan={() => navigate('/pricing')}
/>`}
          </pre>
        </div>
      </div>

      {/* Alert Component */}
      <InsufficientBalanceAlert
        isOpen={isAlertOpen}
        onClose={handleCloseAlert}
        onBuyPlan={handleBuyPlan}
      />
    </div>
  );
};

export default InsufficientBalanceAlertDemo;
