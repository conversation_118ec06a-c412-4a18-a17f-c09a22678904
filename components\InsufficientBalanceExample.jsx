import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import InsufficientBalanceAlert from './InsufficientBalanceAlert';

const InsufficientBalanceExample = () => {
  const navigate = useNavigate();
  const [userBalance, setUserBalance] = useState(50); // Simulated user balance
  const [showAlert, setShowAlert] = useState(false);

  // Simulated premium features with different costs
  const premiumFeatures = [
    { name: 'AI Interview Practice', cost: 100, description: 'Get personalized interview practice with AI feedback' },
    { name: 'Advanced MCQ Generator', cost: 75, description: 'Generate unlimited MCQs for any topic' },
    { name: 'PDF to Video Conversion', cost: 150, description: 'Convert your PDFs into engaging video content' },
    { name: 'Smart Study Planner', cost: 25, description: 'AI-powered study schedule optimization' },
    { name: 'Doubt Resolution', cost: 30, description: 'Get instant answers to your academic questions' },
    { name: 'Live Tutoring Session', cost: 200, description: 'One-on-one session with expert tutors' }
  ];

  const handleFeatureClick = (feature) => {
    if (userBalance < feature.cost) {
      setShowAlert(true);
    } else {
      // Simulate using the feature
      setUserBalance(prev => prev - feature.cost);
      alert(`✅ ${feature.name} activated! Remaining balance: ${userBalance - feature.cost} coins`);
    }
  };

  const handleBuyPlan = () => {
    // Redirect to pricing page or handle buy plan logic
    console.log('Redirecting to pricing page...');
    // navigate('/pricing'); // Uncomment when you have a pricing page
    alert('Redirecting to pricing page...');
  };

  const handleAddCoins = () => {
    setUserBalance(prev => prev + 100);
    alert('100 coins added to your account!');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">AI Classroom Features</h1>
              <p className="text-gray-600 mt-2">Unlock premium features with your coins</p>
            </div>
            <div className="text-right">
              <div className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-3 rounded-lg">
                <div className="text-sm opacity-90">Your Balance</div>
                <div className="text-2xl font-bold">{userBalance} Coins</div>
              </div>
              <button
                onClick={handleAddCoins}
                className="mt-2 text-sm text-blue-600 hover:text-blue-800 underline"
              >
                Add 100 coins (for demo)
              </button>
            </div>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {premiumFeatures.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-semibold text-gray-800">{feature.name}</h3>
                <div className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                  {feature.cost} coins
                </div>
              </div>
              
              <p className="text-gray-600 mb-6">{feature.description}</p>
              
              <button
                onClick={() => handleFeatureClick(feature)}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                  userBalance >= feature.cost
                    ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:opacity-90 hover:scale-105'
                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                }`}
                disabled={userBalance < feature.cost}
              >
                {userBalance >= feature.cost ? 'Use Feature' : 'Insufficient Balance'}
              </button>
            </div>
          ))}
        </div>

        {/* Info Section */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">💰</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">Earn Coins</h3>
              <p className="text-gray-600 text-sm">Complete tasks, quizzes, and activities to earn coins</p>
            </div>
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🚀</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">Use Features</h3>
              <p className="text-gray-600 text-sm">Spend coins to unlock premium AI-powered features</p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">📈</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">Learn More</h3>
              <p className="text-gray-600 text-sm">Get better results with our advanced learning tools</p>
            </div>
          </div>
        </div>
      </div>

      {/* Insufficient Balance Alert */}
      <InsufficientBalanceAlert
        isOpen={showAlert}
        onClose={() => setShowAlert(false)}
        onBuyPlan={handleBuyPlan}
      />
    </div>
  );
};

export default InsufficientBalanceExample;
