import { use, useState, useRef, useEffect } from "react";
import {
  Search,
  Upload,
  DivideCircle,
  Globe,
  UserCircle,
  Menu,
  X,
} from "lucide-react";
import OpenAI from "openai";
import Login from "./Login";
import SignUp from "./Signup";
import { useNavigate } from "react-router-dom";
import HeaderLogo from "./navlogo.png"
import Resource from "./resource.svg";
import Coins from "../coins.png"
import pdfImage from "./pdfFile.png";

const Book = "/book.png";
const Bulb = "/bulb.png";
const DoubleCircle = "/doubleCircle.png";
const Laptop = "/laptop.png";
const PlayButton = "/playButton.png";
const ReactLogo = "/react.png";
const Ruler = "/ruler.png";
const WhiteBoard = "/whiteBoard.png";
const Scale90 = "/Scale90.png";


export default function Home() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [model, setModel] = useState(false);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const [globeselected, setglobeselected] = useState(false);
  const [logged, setLogged] = useState(false);
  const [user, setUser] = useState({});
  const [FILE, setFILE] = useState(null);
  const fileInputRef = useRef(null);
  const fileInputRef2 = useRef(null);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("English");
  const [showDropdown, setShowDropdown] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [debouncedInput, setDebouncedInput] = useState("");
  const handleLogout = () => {
    localStorage.removeItem("auth-token");
    localStorage.removeItem("user-data");
    console.log("Logged out");
    window.location.reload();
  };
  useEffect(() => {
    console.log("Input changed:", input);
    if (debouncedInput.length === 0) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    const fetchSuggestions = async () => {
      try {
        const response = await fetch(
          `https://api.aiclassroom.in/api/suggestions?q=${encodeURIComponent(debouncedInput)}`
        );
        const data = await response.json();
        setSuggestions(data.suggestions || []);
        setShowSuggestions(true);
      } catch (err) {
        console.error("Failed to fetch suggestions", err);
        setSuggestions([]);
        setShowSuggestions(false);
      }
    };

    fetchSuggestions();
  }, [debouncedInput]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedInput(input);
    }, 300);

    return () => clearTimeout(handler);
  }, [input]);

  const languages = [

    "English",

  ];
  useEffect(() => {
    localStorage.setItem("lang", selectedLanguage);
  }, [selectedLanguage]);
  const handleIconClick = () => {
    if (!localStorage.getItem("user-data")) {
      alert("Please login to continue");
      return;
    }
    fileInputRef.current.click(); // Trigger hidden file input
  };
  const navigate = useNavigate();
  const handleIconClick2 = () => {
    if (!localStorage.getItem("user-data")) {
      alert("Please login to continue");
      return;
    }
    navigate("/ai-resource-finder");
    // fileInputRef2.current.click(); // Trigger hidden file input
  };

  const handleFileChange = (event) => {
    if (!localStorage.getItem("user-data")) {
      alert("Please login to continue");
      return;
    }
    const file = event.target.files[0];
    if (file && file.type === "application/pdf") {
      console.log("Selected PDF:", file);
      setFILE(file);
      // You can pass the file to a handler if needed
      navigate("/upload-page", { state: { selectedFile: file } });
    } else {
      alert("Please select a PDF file.");
    }
  };
  const handleSelect = (suggestion) => {
    setInput(suggestion);
    setShowSuggestions(false);
    // You can trigger a search or redirect here
  };

  const handleFileChange2 = (event) => {
    if (!localStorage.getItem("user-data")) {
      alert("Please login to continue");
      return;
    }
    const file = event.target.files[0];
    if (file && file.type === "application/pdf") {
      console.log("Selected PDF:", file);
      setFILE(file);
      // You can pass the file to a handler if needed
      navigate("/ask-doubt", { state: { selectedFile: file } });
    } else {
      alert("Please select a PDF file.");
    }
  };
  useEffect(() => {
    const token = localStorage.getItem("auth-token");
    const userData = localStorage.getItem("user-data");
    if (token) {
      setLogged(true);
      setUser(JSON.parse(userData));
    } else {
      setLogged(false);
    }
  }, []);

  useEffect(() => {
    console.log(user);
  }, [user])
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };
  const searchglobe = async () => {
    setLoading(true);

    const client = new OpenAI({
      apiKey: "********************************************************************************************************************************************************************", dangerouslyAllowBrowser: true
    });

    const response = await client.responses.create({
      model: "gpt-4.1",
      tools: [{
        type: "web_search_preview",
        user_location: {
          type: "approximate",
          country: "IN",
          city: "Delhi",
          region: "Delhi"
        }, search_context_size: "low",
      }],
      input: `Find online data of ${input}`,
    });
    const annotations = []
    for (let i = 1; i < response.output.length; i++) {
      for (let j = 0; j < response.output[i].content.length; j++) {
        for (let k = 0; k < response.output[i].content[j].annotations.length; k++) {
          annotations.push(response.output[i].content[j].annotations[k])
        }
      }
    }
    const data = response.output_text;
    const cleanedData = data.replace(/\(?\[[^\]]+\]\([^)]+\)\)?/g, ' ');

    fetch(`https://api.aiclassroom.in/normalupload`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ query: cleanedData })
    }).then((res) => res.json()).then((data) => {
      navigate("/upload-page", { state: { selectedFile: data.pdfUrl, fileUrl: true, annotations } });
    }).catch((err) => {
      console.log(err);
    })
    console.log("Output Text", data)
    console.log("----Here are the web sites from which data has been scrapped-----")
    for (let i = 0; i < annotations.length; i++) {
      console.log("-------------------")
      console.log("Site No: - ", i + 1)
      console.log("Content Title - ", annotations[i].title);
      console.log("Content Pricked from ", annotations[i].start_index, " To ", annotations[i].end_index);
      console.log("Source Url - ", annotations[i].url.replace("?utm_source=openai", ""))
      console.log("-------------------")
    }
  }
  const [isSignUp, setIsSignUp] = useState(true);
  const nav = useNavigate();
  return (
    <div className="min-h-screen bg-white flex flex-col items-center px-4 md:px-16 relative overflow-hidden">
      {/* Decorative Images - Responsive positioning */}
      {model && (
        <>
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            onClick={() => setModel(false)}
          ></div>
          <div className="fixed inset-0 flex items-center justify-center z-50">
            {isSignUp ? <SignUp setModel={setModel} /> : <Login setModel={setModel} />}
          </div>
        </>
      )}
      <img
        src={Book}
        alt="Book"
        className="absolute top-[50%] left-[10%] md:left-[15%] w-[8vw] max-w-[70px] hidden sm:block z-0"
      />
      <img
        src={Bulb}
        alt="Bulb"
        className="absolute top-[15%] right-[10%] md:right-[15%] w-[8vw] max-w-[70px] hidden sm:block z-0"
      />
      <img
        src={DoubleCircle}
        alt="Double Circle"
        className="absolute top-[15%] left-[5%] md:left-[8%] w-[6vw] max-w-[50px] hidden sm:block z-0"
      />
      <img
        src={Laptop}
        alt="Laptop"
        className="absolute bottom-[5%] right-[2%] w-[25vw] max-w-[300px] hidden md:block z-0"
      />
      <img
        src={PlayButton}
        alt="Play Button"
        className="absolute top-[45%] left-[8%] md:left-[12%] w-[6vw] max-w-[50px] hidden sm:block z-0"
      />
      <img
        src={ReactLogo}
        alt="React Logo"
        className="absolute top-[25%] right-[5%] md:right-[8%] w-[6vw] max-w-[50px] hidden sm:block z-0"
      />
      <img
        src={Ruler}
        alt="Ruler"
        className="absolute top-[22%] left-[20%] md:left-[20%] w-[6vw] max-w-[50px] hidden sm:block z-0"
      />
      <img
        src={Scale90}
        alt="Scale90"
        className="absolute bottom-[5%] left-[50%] md:left-[55%] w-[6vw] max-w-[50px] hidden sm:block z-0"
      />
      <img
        src={WhiteBoard}
        alt="White Board"
        className="absolute bottom-[10%] left-[10%] md:left-[12%] w-[8vw] max-w-[70px] hidden sm:block z-0"
      />

      {/* Header */}
      <header className="w-full py-4 px-4 md:px-6 flex items-center justify-between fixed top-0 left-0 right-0 bg-white z-30">
        {/* Logo */}
        <button onClick={() => nav("/")} className="flex items-center">
          <img
            src={HeaderLogo}
            alt="Logo"
            width={200}
            height={200}
            className="w-[150px] md:w-[100px]"
          />
        </button>

        {/* Mobile Menu Button */}
        <button onClick={toggleMenu} className="md:hidden z-40">
          {isMenuOpen ? (
            <X className="h-7 w-7 text-purple-900" />
          ) : (
            <Menu className="h-7 w-7 text-purple-900" />
          )}
        </button>

        {/* Desktop Menu */}
        <nav className="hidden md:flex items-center gap-6">
          {/* your buttons as is */}
          <button
            onClick={() => {
              if (!localStorage.getItem("user-data")) {
                alert("Please login to continue");
                return;
              }
              nav("/smart-resources");
            }}
            className="text-sm font-medium hover:text-purple-700 cursor-pointer"
          >
            Dashbaord
          </button>
          <button
            onClick={() => {
              if (!localStorage.getItem("user-data")) {
                alert("Please login to continue");
                return;
              } nav("/rewards")
            }}
            className="text-sm font-medium hover:text-purple-700 cursor-pointer"
          >
            Redeem
          </button>
          <button
            onClick={() => {
              window.scrollTo({
                top: 3300,
                behavior: "smooth",
              });
            }}
            className="text-sm cursor-pointer font-medium hover:text-purple-700 transition-colors duration-200"
          >
            Pricing
          </button>
          <button
            onClick={() => nav("/contact")}
            className="text-sm font-medium hover:text-purple-700 cursor-pointer"
          >
            Contact Us
          </button>
          <button
            onClick={() => nav("/contest")}
            className="text-sm font-medium hover:text-purple-700 cursor-pointer"
          >
            Contest
          </button>
          <button
            onClick={() => window.location.href = "https://commercial.aiclassroom.in/"}
            className="text-sm font-medium hover:text-purple-700 cursor-pointer">
            Commercial
          </button>
          {/* Language Toggle */}
          <div className="relative inline-block">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="flex text-md items-center justify-between rounded-full bg-purple-100 border border-purple-300 text-purple-900 p-1 pl-6 h-auto leading-tight focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            >
              {selectedLanguage}
              <svg className="fill-current h-4 w-4 ml-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
              </svg>
            </button>

            {isOpen && (
              <div className="absolute z-50 mt-1 w-full rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                <div className="py-1">
                  {languages.map((language) => (
                    <button
                      key={language}
                      onClick={() => {
                        setSelectedLanguage(language);
                        setIsOpen(false);
                      }}
                      className={`block w-full text-left px-4 py-2 text-md ${selectedLanguage === language
                        ? "bg-purple-100 text-purple-900"
                        : "text-gray-700 hover:bg-gray-100"
                        }`}
                    >
                      {language}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Notification & User */}
          <div className="flex items-center gap-2">
            {logged ? (
              <div className="flex relative cursor-pointer items-center gap-4" onClick={() => setShowDropdown(!showDropdown)}>
                <div className="flex items-center gap-2 bg-purple-100 px-3 py-1 rounded-full">
                  <span className="text-sm font-medium text-purple-900 flex items-center">
                    {Math.floor(user.coins)}
                    <img src={Coins} alt="Coins" style={{ height: 20, width: 20, marginLeft: 4 }} />
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <img src={user.avatar} alt={user.firstName} className="w-8 h-8 rounded-full object-cover" />
                  <span className="text-sm font-medium">{user.firstName}</span>
                </div>

                {showDropdown && (
                  <div className="absolute right-0 mt-32 bg-gray-200 shadow-md rounded-md py-2 z-50">
                    <button
                      onClick={handleLogout}
                      className="w-full text-left font-bold px-4 py-2 text-sm hover:bg-gray-100"
                    >
                      Logout
                    </button>
                    <button
                      onClick={() => navigate("/quiz-results")}
                      className="w-full text-left font-bold px-4 py-2 text-sm hover:bg-gray-100"
                    >
                      See Quiz Results
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div>
                <div className="flex justify-center space-x-4">
                  <button
                    className={`py-1 px-4 rounded-md text-sm font-medium cursor-pointer ${isSignUp ? "bg-purple-600 text-white" : "bg-gray-200 text-gray-700"
                      }`}
                    onClick={() => {
                      window.location.href = "/newsignup"
                    }}
                  >
                    Sign Up
                  </button>
                  <button
                    className={`py-1 px-4 rounded-md text-sm font-medium cursor-pointer ${!isSignUp ? "bg-purple-600 text-white" : "bg-gray-200 text-gray-700"
                      }`}
                    onClick={() => {
                      setIsSignUp(false);
                      setModel(true);
                    }}
                  >
                    Login
                  </button>
                </div>

              </div>
            )}
          </div>
        </nav>

        {/* Mobile Menu Overlay */}
        <div
          className={`fixed inset-0 bg-black/50 transition-opacity duration-300 ${isMenuOpen ? "opacity-100 visible" : "opacity-0 invisible"
            } z-40`}
          onClick={toggleMenu}
        ></div>

        <div
          className={`fixed top-0 right-0 w-2/3 max-w-xs h-full bg-white shadow-lg transform transition-transform duration-300 ${isMenuOpen ? "translate-x-0" : "translate-x-full"
            } md:hidden flex flex-col p-6 z-50`}
        >
          {/* Mobile Menu Items */}
          <nav className="mt-12 space-y-6">
            <button
              onClick={() => {
                if (!localStorage.getItem("user-data")) {
                  alert("Please login to continue");
                  return;
                }
                nav("/smart-resources")
              }}
              className="block text-lg font-medium cursor-pointer text-gray-900 hover:text-purple-700"
            >
              Dashboard
            </button>
            <button
              onClick={() => nav("/rewards")}
              className="block text-lg font-medium cursor-pointer text-gray-900 hover:text-purple-700"
            >
              Redeem
            </button>
            <button
              onClick={() => {
                toggleMenu();
                window.scrollTo({
                  top: 4300,
                  behavior: "smooth",
                });
              }}
              className="block text-lg font-medium text-gray-900 hover:text-purple-700"
            >
              Pricing
            </button>
            <button
              onClick={() => {
                nav("/contact");
                toggleMenu();
              }}
              className="block text-lg font-medium cursor-pointer text-gray-900 hover:text-purple-700"
            >
              Contact Us
            </button>
            <button
              onClick={() => nav("/contest")}
              className="block text-lg font-medium cursor-pointer text-gray-900 hover:text-purple-700"
            >
              Contest
            </button>

            {/* Language Toggle 
            <div className="pt-4 border-t">
              <div className="flex items-center justify-center rounded-full bg-purple-100 p-1 w-fit mx-auto">
                <button
                  variant="ghost"
                  className="rounded-full px-4 py-1 h-auto bg-purple-900 text-white text-xs"
                >
                  English
                </button>
                <button
                  variant="ghost"
                  className="rounded-full px-4 py-1 h-auto text-xs"
                >
                  Hindi
                </button>
              </div>
            </div>
*/}
            {/* Notification & User */}
            {/* Inside your mobile menu */}
            <div className="flex justify-between mt-6">
              {logged ? (
                <>
                  <div className=" bg-purple-100 text-purple-900 rounded-full px-3 py-1">
                    <span className="text-sm font-medium text-purple-900 flex items-center">
                      {Math.floor(user.coins)}
                      <img src={Coins} alt="Coins" style={{ height: 20, width: 20, marginLeft: 4 }} />

                    </span>
                  </div>

                  <div
                    className="relative w-fit cursor-pointer"
                    onClick={() => setShowDropdown(!showDropdown)}
                  >
                    <div className="flex items-center gap-2">
                      <img
                        src={user.avatar}
                        alt={user.firstName}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <span className="text-sm font-medium">
                        {user.firstName}
                      </span>
                    </div>
                    {showDropdown && (
                      <div className="absolute right-0 mt-2 w-[150px] bg-gray-200 shadow-md rounded-md py-2 z-2">
                        <div
                          onClick={() => navigate("/quiz-results")}
                          className="w-full text-left font-bold px-4 text-sm hover:bg-gray-100 cursor-pointer"
                        >
                          See Quiz Results
                        </div>
                        <div
                          onClick={handleLogout}
                          className="w-full text-left font-bold px-4 mt-2 text-sm cursor-pointer hover:bg-gray-100"
                        >
                          Logout
                        </div>
                      </div>
                    )}
                  </div>
                </>
              ) : (
                <div className="flex gap-2 w-full justify-center">
                  <button
                    className="py-1 px-4 rounded-md text-sm font-medium bg-purple-600 text-white"
                    onClick={() => {
                      window.location.href = "/newsignup";
                    }}
                  >
                    Sign Up
                  </button>
                  <button
                    className="py-1 px-4 rounded-md text-sm font-medium bg-gray-200 text-gray-700"
                    onClick={() => {

                      toggleMenu();
                      setTimeout(() => {
                        setIsSignUp(false);
                        setModel(true);
                      }, 100);
                    }}
                  >
                    Login
                  </button>
                </div>
              )}
            </div>
          </nav>        </div>
      </header>

      {/* Main Content */}
      <main className="text-center mt-[100px] max-w-2xl px-4 z-10">
        <h2 className="text-2xl md:text-4xl font-bold text-purple-700">
          Welcome To AI Classroom
        </h2>
        <p className="text-lg md:text-[21px] text-gray-700 font-semibold mt-2">
          Classroom in your study table
        </p>
        <p className="text-gray-600 mt-4 text-sm md:text-base">
          Transform Your Textbooks Into Engaging Animated Videos With Our
          AI-Driven Platform.
          <br className="hidden md:block" />
          Enjoy Real-Time Video Summaries.
        </p>
      </main>

      {/* Search Box */}
      {!loading ? (
        <div className="mt-8 w-full max-w-2xl px-4 relative z-20">
          <div className="flex items-center bg-white shadow-md rounded-full p-2">
            <input
              type="text"
              value={input}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  if (globeselected) { searchglobe(); return }
                  console.log(globeselected)
                  if (!localStorage.getItem("user-data")) {
                    alert("Please login to continue");
                    return;
                  }
                  setLoading(true);
                  fetch(`https://api.aiclassroom.in/convertupload`, {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json"
                    },
                    body: JSON.stringify({ query: input })
                  }).then((res) => res.json()).then((data) => {
                    navigate("/upload-page", { state: { selectedFile: data.pdfUrl, fileUrl: true } });
                  }).catch((err) => {
                    console.log(err);
                  })
                }
                if (e.key === "Escape") {
                  setShowSuggestions(false);
                }
              }
              }
              onChange={(e) => setInput(e.target.value)}
              placeholder="Search for topics, chapters, or keywords..."
              className="w-full px-4 py-2 rounded-full focus:outline-none focus:ring-2 focus:ring-purple-600"
            />
            <div className={`relative group mx-2 cursor-pointer text-gray-500 hover:text-purple-600 ${globeselected ? "text-purple-600" : ""} transition`}>
              <button onClick={() => { setglobeselected(!globeselected) }}>
                <Globe size={20} />
              </button>
              <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:flex bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-50">
                Web Search
              </div>
            </div>

            <button
              className="bg-purple-600 text-white rounded-full p-2 ml-2"
              onClick={async () => {
                if (globeselected) { searchglobe(); return }
                if (!localStorage.getItem("user-data")) {
                  alert("Please login to continue");
                  return;
                }
                setLoading(true);
                await fetch(`https://api.aiclassroom.in/convertupload`, {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json"
                  },
                  body: JSON.stringify({ query: input })
                }).then((res) => res.json()).then((data) => {

                  navigate("/upload-page", { state: { selectedFile: data.pdfUrl, fileUrl: true } });
                }).catch((err) => {
                  console.log(err);
                })
              }}
            >
              <Search size={20} />
            </button>
          </div>

          {showSuggestions && suggestions.length > 0 && (
            <ul className="absolute top-full mt-2 w-full bg-white shadow-lg rounded-lg z-50 max-h-60 overflow-y-auto">
              {suggestions.map((s, index) => (
                <li
                  key={index}
                  onClick={() => handleSelect(s)}
                  className="px-4 py-2 hover:bg-purple-100 cursor-pointer"
                >
                  {s}
                </li>
              ))}
            </ul>
          )}
        </div>) : (
        <div className="flex justify-center items-center bg-white shadow-md rounded-full p-4 text-purple-600 font-semibold text-lg">
          <svg
            className="animate-spin h-5 w-5 mr-2 text-purple-600"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8v4l3-3-3-3v4a8 8 0 00-8 8h4z"
            ></path>
          </svg>
          {globeselected ? "Searching Web" : "Loading..."}
        </div>
      )}

      {/* Action Cards */}
      <div className="mt-10 grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6 w-full max-w-xl px-4 z-10">
        <div className="bg-white shadow-md rounded-lg p-4 md:p-6 flex flex-col items-center text-center">
          {/* Hidden input */}
          <input
            type="file"
            accept="application/pdf"
            ref={fileInputRef}
            onChange={handleFileChange} // handleFileChange
            style={{ display: "none" }}
          />

          {/* Upload Icon */}
          <img src={pdfImage}
            size={30}
            className="text-red-500 mb-3 cursor-pointer"
            onClick={handleIconClick}
          />

          <h3 className="text-base md:text-lg font-semibold mb-3">
            Upload Your Files for Animation
          </h3>

          {/* Upload Button */}
          <button
            onClick={handleIconClick}
            className="bg-purple-600 text-white px-3 py-1.5 md:px-4 md:py-2 rounded-md hover:bg-purple-700 text-sm"
          >
            Upload
          </button>
        </div>

        <div className="bg-white shadow-md rounded-lg p-4 md:p-6 flex flex-col items-center text-center">
          {/* Hidden input */}
          <input
            type="file"
            accept="application/pdf"
            ref={fileInputRef2}
            onChange={handleFileChange2
            }
            style={{ display: "none" }}
          />
          <img src={Resource} size={30} className="text-purple-500 mb-3" />
          <h3 className="text-base md:text-xl font-semibold mb-3">
            Smart Resource Finder
          </h3>
          <button
            onClick={handleIconClick2}
            className="bg-purple-600 text-white px-3 py-3 md:px-4 md:py-2 rounded-md hover:bg-purple-700 text-sm mt-4">
            Explore
          </button>
        </div>      </div>

      {/* Background Decorations */}
      <div className="absolute top-0 left-0 opacity-20 z-0">
        <svg width="150" height="150" className="text-purple-200">
          <circle cx="50" cy="50" r="40" fill="currentColor" />
        </svg>
      </div>
      <div className="absolute bottom-0 right-0 opacity-20 z-0">
        <svg width="150" height="150" className="text-blue-200">
          <circle cx="150" cy="150" r="40" fill="currentColor" />
        </svg>
      </div>
    </div>
  );
};