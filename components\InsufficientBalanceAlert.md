# InsufficientBalanceAlert Component

A reusable React component that displays an alert modal when users have insufficient balance to perform an action. The component follows the Figma design specifications with a gradient background, purple heading, and gradient button.

## Features

- ✅ Matches Figma design specifications exactly
- ✅ Fully responsive and mobile-friendly
- ✅ Customizable text content
- ✅ Configurable callbacks for close and buy plan actions
- ✅ Smooth animations and hover effects
- ✅ Accessible with proper ARIA labels
- ✅ Z-index management for proper layering

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `isOpen` | boolean | - | **Required.** Controls whether the alert is visible |
| `onClose` | function | - | **Required.** Callback function when alert is closed |
| `onBuyPlan` | function | - | Optional. Callback function when "Buy a Plan" button is clicked |
| `title` | string | "Oops !" | Optional. The main heading text |
| `message` | string | "Insufficient Balance!\nCannot Perform This Action Now." | Optional. The message text (supports line breaks with \n) |
| `buttonText` | string | "Buy a Plan" | Optional. Text for the action button |

## Basic Usage

```jsx
import React, { useState } from 'react';
import InsufficientBalanceAlert from './components/InsufficientBalanceAlert';

function MyComponent() {
  const [showAlert, setShowAlert] = useState(false);

  const handleInsufficientBalance = () => {
    setShowAlert(true);
  };

  const handleBuyPlan = () => {
    // Redirect to pricing page or handle buy plan logic
    window.location.href = '/pricing';
  };

  return (
    <div>
      <button onClick={handleInsufficientBalance}>
        Trigger Action
      </button>

      <InsufficientBalanceAlert
        isOpen={showAlert}
        onClose={() => setShowAlert(false)}
        onBuyPlan={handleBuyPlan}
      />
    </div>
  );
}
```

## Advanced Usage with Custom Content

```jsx
<InsufficientBalanceAlert
  isOpen={showAlert}
  onClose={() => setShowAlert(false)}
  onBuyPlan={() => navigate('/subscription')}
  title="Not Enough Coins!"
  message="You need more coins to access this feature.\nPlease purchase a plan to continue."
  buttonText="Get More Coins"
/>
```

## Integration Examples

### With React Router

```jsx
import { useNavigate } from 'react-router-dom';

function MyComponent() {
  const navigate = useNavigate();
  const [showAlert, setShowAlert] = useState(false);

  const handleBuyPlan = () => {
    navigate('/pricing');
  };

  // ... rest of component
}
```

### With Balance Check

```jsx
function MyComponent() {
  const [userBalance, setUserBalance] = useState(0);
  const [showAlert, setShowAlert] = useState(false);

  const handlePremiumAction = () => {
    const requiredBalance = 100;
    
    if (userBalance < requiredBalance) {
      setShowAlert(true);
    } else {
      // Proceed with the action
      performPremiumAction();
    }
  };

  // ... rest of component
}
```

## Styling

The component uses inline styles to match the exact Figma specifications:

- **Background**: Linear gradient from #EEF2FF to #FAF5FF
- **Border**: Gradient border with opacity
- **Shadow**: 2.125px 4.25px 8.5px rgba(0, 0, 0, 0.2)
- **Title Color**: #3A1078 (purple)
- **Message Color**: #4B5563 (gray)
- **Button**: Linear gradient from #6BA0FF to #755BFF

## Accessibility

- Proper ARIA labels for screen readers
- Keyboard navigation support
- Focus management
- Semantic HTML structure

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design for all screen sizes

## Demo

To see the component in action, import and use the `InsufficientBalanceAlertDemo` component:

```jsx
import InsufficientBalanceAlertDemo from './components/InsufficientBalanceAlertDemo';

function App() {
  return <InsufficientBalanceAlertDemo />;
}
```
