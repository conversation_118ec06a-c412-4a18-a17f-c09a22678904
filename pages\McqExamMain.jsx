import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

const MCQExam = () => {
    // State management
    const [quizData, setQuizData] = useState(null);
    const [currentQuestion, setCurrentQuestion] = useState(1);
    const [selectedAnswers, setSelectedAnswers] = useState({});
    const [markedForReview, setMarkedForReview] = useState(new Set());
    const [timeLeft, setTimeLeft] = useState(0);
    const [loading, setLoading] = useState(true);
    const [showSubmitModal, setShowSubmitModal] = useState(false);
    const [error, setError] = useState(null);

    const navigate = useNavigate();
    const location = useLocation();
    const timerRef = useRef(null);

    // Initialize quiz data from navigation state or localStorage
    useEffect(() => {
        const initializeQuiz = () => {
            try {
                let quizDataToUse = null;
                let topicName = '';
                let duration = 0;
                let totalQuestions = 0;
                let questions = [];

                // First, try to get data from navigation state (primary method)
                if (location.state) {
                    const { questionsList, examDetails } = location.state;
                    const { timeLimit, topic, totalQuestions: navTotal } = examDetails;
                    
                    if (questionsList && questionsList.length > 0) {
                        questions = questionsList;
                        topicName = topic || 'Quiz Topic';
                        duration = Math.floor(timeLimit / 60); // Convert seconds to minutes
                        totalQuestions = navTotal || questionsList.length;
                        
                        // Save to localStorage for session persistence
                        localStorage.setItem('mcq_quiz_data', JSON.stringify({ questions }));
                        localStorage.setItem('mcq_quiz_topic', topicName);
                        localStorage.setItem('mcq_quiz_duration', duration.toString());
                        localStorage.setItem('mcq_quiz_total_questions', totalQuestions.toString());
                    }
                }

                // If no navigation state, try localStorage (fallback method)
                if (!questions.length) {
                    const savedQuizData = localStorage.getItem('mcq_quiz_data');
                    const savedTopic = localStorage.getItem('mcq_quiz_topic');
                    const savedDuration = localStorage.getItem('mcq_quiz_duration');
                    const savedTotalQuestions = localStorage.getItem('mcq_quiz_total_questions');
                    
                    if (!savedQuizData || !savedTopic || !savedDuration || !savedTotalQuestions) {
                        setError('No quiz data found. Please create a quiz first.');
                        setLoading(false);
                        return;
                    }

                    const parsedQuizData = JSON.parse(savedQuizData);
                    questions = parsedQuizData.questions || [];
                    topicName = savedTopic;
                    duration = parseInt(savedDuration, 10);
                    totalQuestions = parseInt(savedTotalQuestions, 10);
                }

                // Validate quiz data
                if (!questions || questions.length === 0) {
                    setError('No questions found in quiz data.');
                    setLoading(false);
                    return;
                }

                // Create complete quiz data object
                quizDataToUse = {
                    questions,
                    topic: topicName,
                    duration: duration,
                    totalQuestions: totalQuestions,
                    totalMarks: totalQuestions // Assuming each question is worth 1 mark
                };

                setQuizData(quizDataToUse);
                
                // Check if there's a saved session in progress
                const savedStartTime = localStorage.getItem('mcq_quiz_start_time');
                const savedSessionTopic = localStorage.getItem('mcq_quiz_session_topic');
                
                // Only resume if it's the same topic/session
                if (savedStartTime && savedSessionTopic === topicName) {
                    // Resume existing session
                    const startTime = parseInt(savedStartTime, 10);
                    const elapsed = Math.floor((Date.now() - startTime) / 1000);
                    const remainingTime = Math.max(0, duration * 60 - elapsed); // Convert minutes to seconds
                    
                    setTimeLeft(remainingTime);
                    
                    // Restore saved state
                    const savedAnswers = localStorage.getItem('mcq_quiz_answers');
                    const savedMarkedForReview = localStorage.getItem('mcq_quiz_marked_for_review');
                    const savedCurrentQuestion = localStorage.getItem('mcq_quiz_current_question');
                    
                    if (savedAnswers) {
                        try {
                            setSelectedAnswers(JSON.parse(savedAnswers));
                        } catch (error) {
                            console.error('Error parsing saved answers:', error);
                        }
                    }
                    
                    if (savedMarkedForReview) {
                        try {
                            setMarkedForReview(new Set(JSON.parse(savedMarkedForReview)));
                        } catch (error) {
                            console.error('Error parsing marked for review:', error);
                        }
                    }
                    
                    if (savedCurrentQuestion) {
                        setCurrentQuestion(parseInt(savedCurrentQuestion, 10));
                    }
                } else {
                    // Start new session
                    const initialTime = duration * 60; // Convert minutes to seconds
                    setTimeLeft(initialTime);
                    localStorage.setItem('mcq_quiz_start_time', Date.now().toString());
                    localStorage.setItem('mcq_quiz_session_topic', topicName);
                    setCurrentQuestion(1);
                    setSelectedAnswers({});
                    setMarkedForReview(new Set());
                    
                    // Clear any old session data
                    localStorage.removeItem('mcq_quiz_answers');
                    localStorage.removeItem('mcq_quiz_marked_for_review');
                    localStorage.removeItem('mcq_quiz_current_question');
                }
                
                setLoading(false);
            } catch (error) {
                console.error('Error initializing quiz:', error);
                setError('Error loading quiz data. Please try again.');
                setLoading(false);
            }
        };

        initializeQuiz();
    }, [location.state]);

    // Timer countdown
    useEffect(() => {
        if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
        }

        if (loading || timeLeft <= 0) return;

        timerRef.current = setInterval(() => {
            setTimeLeft(prev => {
                if (prev <= 1) {
                    clearInterval(timerRef.current);
                    setShowSubmitModal(true);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
        };
    }, [loading, timeLeft]);

    // Save state to localStorage
    useEffect(() => {
        if (!loading && quizData) {
            localStorage.setItem('mcq_quiz_answers', JSON.stringify(selectedAnswers));
            localStorage.setItem('mcq_quiz_marked_for_review', JSON.stringify([...markedForReview]));
            localStorage.setItem('mcq_quiz_current_question', currentQuestion.toString());
        }
    }, [selectedAnswers, markedForReview, currentQuestion, loading, quizData]);

    // Cleanup timer on component unmount
    useEffect(() => {
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
        };
    }, []);

    // Helper functions
    const formatTime = (seconds) => {
        const hrs = Math.floor(seconds / 3600);
        const mins = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    const getAttemptedCount = () => {
        return Object.keys(selectedAnswers).length;
    };

    const getQuestionStatus = (questionNumber) => {
        if (questionNumber === currentQuestion) return 'current';
        if (markedForReview.has(questionNumber)) return 'marked';
        if (selectedAnswers[questionNumber] !== undefined) return 'answered';
        return 'unanswered';
    };

    const handleAnswerSelect = (questionNumber, optionIndex) => {
        setSelectedAnswers(prev => ({
            ...prev,
            [questionNumber]: optionIndex
        }));
    };

    const handleSkip = () => {
        if (currentQuestion < quizData.totalQuestions) {
            setCurrentQuestion(prev => prev + 1);
        }
    };

    const handleMarkForReview = () => {
        setMarkedForReview(prev => {
            const newSet = new Set(prev);
            if (newSet.has(currentQuestion)) {
                newSet.delete(currentQuestion);
            } else {
                newSet.add(currentQuestion);
            }
            return newSet;
        });
    };

    const handleProceed = () => {
        if (currentQuestion < quizData.totalQuestions) {
            setCurrentQuestion(prev => prev + 1);
        }
    };

    const handleQuestionNavigation = (questionNumber) => {
        setCurrentQuestion(questionNumber);
    };

    const handleSubmitQuiz = () => {
        setShowSubmitModal(true);
    };

    const confirmSubmit = () => {
        // Calculate score
        let score = 0;
        quizData.questions.forEach(question => {
            // Handle both correctAnswer (index) and correct (letter) formats
            let correctIndex = question.correctAnswer;
            if (correctIndex === undefined && question.correct) {
                // Convert letter to index (a=0, b=1, c=2, d=3)
                correctIndex = ['a', 'b', 'c', 'd'].indexOf(question.correct.toLowerCase());
            }
            
            if (selectedAnswers[question.qNo] === correctIndex) {
                score++;
            }
        });

        // Calculate time taken
        const startTime = parseInt(localStorage.getItem('mcq_quiz_start_time'), 10);
        const timeTaken = Math.floor((Date.now() - startTime) / 1000);

        // Save results to localStorage
        const results = {
            score,
            total: quizData.totalQuestions,
            topic: quizData.topic,
            timeTaken,
            totalTime: quizData.duration * 60, // in seconds
            answers: selectedAnswers,
            questions: quizData.questions
        };

        localStorage.setItem('mcq_quiz_results', JSON.stringify(results));

        // Clear quiz session data
        localStorage.removeItem('mcq_quiz_answers');
        localStorage.removeItem('mcq_quiz_marked_for_review');
        localStorage.removeItem('mcq_quiz_current_question');
        localStorage.removeItem('mcq_quiz_start_time');
        localStorage.removeItem('mcq_quiz_session_topic');
        localStorage.removeItem('mcq_quiz_data');
        localStorage.removeItem('mcq_quiz_duration');
        localStorage.removeItem('mcq_quiz_topic');
        localStorage.removeItem('mcq_quiz_total_questions');

        // Navigate to results page
        navigate('/mcq-results');
    };

    const handleGoBack = () => {
        // Clear quiz session data
        localStorage.removeItem('mcq_quiz_answers');
        localStorage.removeItem('mcq_quiz_marked_for_review');
        localStorage.removeItem('mcq_quiz_current_question');
        localStorage.removeItem('mcq_quiz_start_time');
        localStorage.removeItem('mcq_quiz_session_topic');
        localStorage.removeItem('mcq_quiz_data');
        localStorage.removeItem('mcq_quiz_duration');
        localStorage.removeItem('mcq_quiz_topic');
        localStorage.removeItem('mcq_quiz_total_questions');
        
        navigate('/');
    };

    // Loading state
    if (loading) {
        return (
            <div className="min-h-screen bg-[#F9FDFF] flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-lg text-gray-600">Loading quiz...</p>
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className="min-h-screen bg-[#F9FDFF] flex items-center justify-center">
                <div className="text-center max-w-md mx-4">
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        <p className="font-bold">Error</p>
                        <p>{error}</p>
                    </div>
                    <button
                        onClick={handleGoBack}
                        className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Go Back to Create Quiz
                    </button>
                </div>
            </div>
        );
    }

    // No quiz data
    if (!quizData) {
        return (
            <div className="min-h-screen bg-[#F9FDFF] flex items-center justify-center">
                <div className="text-center max-w-md mx-4">
                    <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
                        <p className="font-bold">No Quiz Data</p>
                        <p>Please create a quiz first to start the exam.</p>
                    </div>
                    <button
                        onClick={handleGoBack}
                        className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Create Quiz
                    </button>
                </div>
            </div>
        );
    }

    const currentQuestionData = quizData.questions.find(q => q.qNo === currentQuestion) || quizData.questions[0];

    return (
        <div className="min-h-screen bg-[#F9FDFF] p-6">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="text-center mb-8">
                    <h1 className="text-4xl font-medium text-[#3A1078] mt-6 mb-6 font-poppins">
                        Topic: {quizData.topic}
                    </h1>
                    <p className="text-xl text-[#3A1078] mt-6 font-medium font-poppins">
                        Choose the correct option, and submit your quiz after reviewing all questions.
                    </p>
                    <p className="text-m text-[#c44b4b] mt-6 font-medium font-poppins">
                        ⚠ Note: Refreshing this page will erase your quiz progress.
                    </p>
                </div>

                {/* Main Quiz Interface */}
                <div className="flex gap-8 mt-16">
                    {/* Left Section - Question Display */}
                    <div className="flex-1">
                        <div className="bg-white rounded-[38px] shadow-lg overflow-hidden">
                            {/* Question Header */}
                            <div className="bg-[#4C60A5] text-white px-6 py-4 flex justify-between items-center">
                                <h2 className="text-3xl font-medium font-poppins">Question {currentQuestion}</h2>
                                <span className="text-lg font-medium font-poppins">{currentQuestion}/{quizData.totalQuestions}</span>
                            </div>

                            {/* Question Content */}
                            <div className="px-8 pt-8">
                                <h3 className="text-2xl font-medium text-black mb-8 font-advent-pro">
                                    {currentQuestionData.Question}
                                </h3>

                                {/* Options */}
                                <div className="space-y-4">
                                    {currentQuestionData.options.map((option, index) => {
                                        const isSelected = selectedAnswers[currentQuestionData.qNo] === index;
                                        return (
                                            <div key={index} className="flex items-center gap-4">
                                                <div className="relative">
                                                    <div className={`w-6 h-6 rounded-full border-3 ${isSelected ? 'border-black' : 'border-gray-400'}`}>
                                                        {isSelected && (
                                                            <div className="w-3.5 h-3.5 bg-[#4182F9] rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                                                        )}
                                                    </div>
                                                </div>
                                                <button
                                                    onClick={() => handleAnswerSelect(currentQuestionData.qNo, index)}
                                                    className="text-xl font-medium text-black font-advent-pro text-left hover:text-[#4182F9] transition-colors"
                                                >
                                                    {option}
                                                </button>
                                            </div>
                                        );
                                    })}
                                </div>

                                {/* Navigation Buttons */}
                                <div className="flex justify-between gap-4 mt-16">
                                    <button
                                        onClick={handleSkip}
                                        className="px-8 py-3 bg-[#342499] text-white rounded-lg font-bold text-base font-poppins hover:bg-[#2A1A7A] transition-colors"
                                    >
                                        Skip
                                    </button>
                                    <div className='flex gap-4'>
                                        <button
                                            onClick={handleMarkForReview}
                                            className={`px-8 py-3 rounded-lg font-bold text-base font-poppins transition-colors ${markedForReview.has(currentQuestion)
                                                ? 'bg-[#D4A017] text-white hover:bg-[#B8900F]'
                                                : 'bg-[#AE7A01] text-white hover:bg-[#8B6001]'
                                                }`}
                                        >
                                            {markedForReview.has(currentQuestion) ? 'Unmark Review' : 'Mark For Review'}
                                        </button>
                                        <button
                                            onClick={handleProceed}
                                            disabled={currentQuestion >= quizData.totalQuestions}
                                            className="px-8 py-3 bg-[#217C58] text-white rounded-lg font-bold text-base font-poppins hover:bg-[#1A6347] disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                                        >
                                            Proceed
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {/* Bottom Info */}
                            <div className='bg-[#4C60A5] w-full pb-4'>
                                <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
                                    <span className="text-xl font-normal text-white bg-[#4C60A5] px-4 py-2 rounded font-jost">
                                        Total Number Of Questions: {quizData.totalQuestions}
                                    </span>
                                    <span className="text-xl font-normal text-white bg-[#4C60A5] px-4 py-2 rounded font-jost">
                                        Total Marks: {quizData.totalMarks}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right Section - Timer and Navigation */}
                    <div className="w-90">
                        <div className="bg-white rounded-lg shadow-lg p-6 space-y-6 border-2 border-gray-200">
                            {/* Timer */}
                            <div className="text-center">
                                <h3 className="text-xl font-bold text-black mb-2 font-jost">Time Left</h3>
                                <div
                                    className={`text-4xl font-extrabold font-jost ${timeLeft <= 60
                                        ? 'text-red-600'
                                        : timeLeft <= 120
                                            ? 'text-yellow-600'
                                            : 'text-[#08A064]'
                                        }`}
                                >
                                    {formatTime(timeLeft)}
                                </div>
                            </div>

                            {/* Attempted Count */}
                            <div className="text-center">
                                <p className="text-xl font-semibold text-black font-poppins">
                                    Attempted: {getAttemptedCount()}/{quizData.totalQuestions}
                                </p>
                            </div>

                            {/* Question Navigation Grid */}
                            <div className="h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200">
                                <div className="space-y-3 pr-2">
                                    {Array.from({ length: Math.ceil(quizData.totalQuestions / 6) }, (_, rowIndex) => (
                                        <div key={rowIndex} className="flex gap-2">
                                            {Array.from({ length: 6 }, (_, colIndex) => {
                                                const questionNumber = rowIndex * 6 + colIndex + 1;
                                                if (questionNumber > quizData.totalQuestions) return null;

                                                const status = getQuestionStatus(questionNumber);
                                                const getButtonStyle = () => {
                                                    switch (status) {
                                                        case 'current':
                                                            return 'bg-[#4D8BD7] text-white'; // Blue for current
                                                        case 'answered':
                                                            return 'bg-[#217C58] text-white'; // Green for answered
                                                        case 'marked':
                                                            return 'bg-[#AE7A01] text-white'; // Orange for marked
                                                        default:
                                                            return 'bg-[#5C5C5D] text-white'; // Gray for unanswered
                                                    }
                                                };

                                                return (
                                                    <button
                                                        key={questionNumber}
                                                        onClick={() => handleQuestionNavigation(questionNumber)}
                                                        className={`w-12 h-12 rounded-lg font-normal text-xl font-prompt ${getButtonStyle()} hover:opacity-80 transition-opacity shadow-md`}
                                                    >
                                                        {questionNumber}
                                                    </button>
                                                );
                                            })}
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Submit Button */}
                            <button
                                onClick={handleSubmitQuiz}
                                className="w-full py-4 bg-[#005FD0] text-white rounded-lg font-medium text-lg font-afacad hover:bg-[#004BB0] transition-colors shadow-md"
                            >
                                Confirm and Submit
                            </button>
                        </div>
                    </div>
                </div>

                {/* Submit Confirmation Modal */}
                {showSubmitModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-8 max-w-md w-full mx-4">
                            <h3 className="text-xl font-bold text-[#3A1078] mb-4">Confirm Submission</h3>
                            <p className="text-gray-600 mb-6">
                                Are you sure you want to submit your quiz? You have answered {getAttemptedCount()} out of {quizData.totalQuestions} questions.
                            </p>
                            <div className="flex gap-4">
                                <button
                                    onClick={() => setShowSubmitModal(false)}
                                    className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                                    disabled={timeLeft <= 0}
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={confirmSubmit}
                                    className="flex-1 px-4 py-2 bg-[#005FD0] text-white rounded-lg hover:bg-[#004BB0] transition-colors"
                                >
                                    Submit Quiz
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MCQExam;