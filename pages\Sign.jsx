import { useState, useRef } from "react";
import "./sign.css"; // Keep this if other styles are still in use
import { useNavigate } from "react-router-dom";
import { AlertTriangle } from "react-feather";

const CreateAccount = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    mobile: "",
    password: "",
    confirmPassword: "",
    agreeTerms: false,
  });

  const [errors, setErrors] = useState({
    email: "",
    passwordMatch: "",
    mobile: ""
  });

  const [showOTP, setShowOTP] = useState(false);
  const [otp, setOtp] = useState(["", "", "", ""]);
  const [generatedOTP, setGeneratedOTP] = useState("");
  const inputRefs = useRef([]);
  const [countryCode, setCountryCode] = useState("+91");

  const countryCodes = [
    { value: "+91", label: "IN (+91)" },
    { value: "+1", label: "US (+1)" },
    { value: "+44", label: "UK (+44)" },
    { value: "+61", label: "AU (+61)" },
  ];

  const validateEmail = (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Clear specific error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
    if ((name === "password" || name === "confirmPassword") && errors.passwordMatch) {
      setErrors(prev => ({ ...prev, passwordMatch: "" }));
    }

    let newFormData = { ...formData };

    if (name === "mobile") {
      // Allow only digits and update the state
      if (/^\d*$/.test(value)) {
        newFormData[name] = value;
      } else {
        // If non-digit is entered, don't update the state
        return;
      }
    } else if (name === "countryCode") {
      setCountryCode(value);
      return;
    } else {
      newFormData[name] = type === "checkbox" ? checked : value;
    }

    setFormData(newFormData);

    // Live Validation
    if (name === "email") {
      if (value && !validateEmail(value)) {
        setErrors(prev => ({ ...prev, email: "Please enter a valid email address" }));
      } else {
        setErrors(prev => ({ ...prev, email: "" }));
      }
    }

    if (name === 'confirmPassword' || name === 'password') {
      const pass = name === 'password' ? value : newFormData.password;
      const confPass = name === 'confirmPassword' ? value : newFormData.confirmPassword;
      if (pass && confPass && pass !== confPass) {
        setErrors(prev => ({ ...prev, passwordMatch: "Passwords do not match" }));
      } else {
        setErrors(prev => ({ ...prev, passwordMatch: "" }));
      }
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    let formIsValid = true;
    const newErrors = { email: "", passwordMatch: "", mobile: "" };

    if (!validateEmail(formData.email)) {
      newErrors.email = "Please enter a valid email address";
      formIsValid = false;
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.passwordMatch = "Passwords do not match";
      formIsValid = false;
    }

    if (formData.mobile.length === 0) {
      newErrors.mobile = "Please enter a mobile number";
      formIsValid = false;
    } else if (formData.mobile.length !== 10) {
      newErrors.mobile = "Please enter a 10-digit mobile number";
      formIsValid = false;
    }

    setErrors(newErrors);

    if (!formData.agreeTerms) {
      alert("You must agree to the terms and conditions");
      formIsValid = false;
    }

    if (!formIsValid) {
      return;
    }

    const otpValue = Math.floor(1000 + Math.random() * 9000).toString();
    console.log("otpValue", otpValue);
    setGeneratedOTP(otpValue);

    const sendOtp = async () => {
      const response = await fetch("https://api.aiclassroom.in/sent-otp", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: formData.email, otp: otpValue }),
      });

      console.log("response of sendotp", response);

      if (!response.ok) {
        alert("❌ Failed to send OTP");
        return;
      }

      const result = await response.json();
      console.log("✅ OTP Sent:", result.message);
    };

    sendOtp();
    console.log("Generated OTP:", otpValue);
    setShowOTP(true);
  };

  const handleOtpChange = (index, value) => {
    if (/^\d?$/.test(value)) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value && index < 3) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace" && otp[index] === "") {
      if (index > 0) inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOTP = () => {
    const enteredOtp = otp.join("");
    if (enteredOtp === generatedOTP) {
      alert("OTP verified successfully!");
      navigate("/education-details", {
        state: {
          name: formData.fullName,
          email: formData.email,
          mobile: countryCode + formData.mobile,
          password: formData.password,
        },
      });
    } else {
      alert("Incorrect OTP. Please try again.");
    }
  };

  return (
    <div className="container1">
      <div className="form-container">
        <div className="form-content">
          <h1 className="form-title">Create Your Account</h1>
          <p className="form-subtitle">Join AI Classroom to start your personalized learning journey.</p>

          {!showOTP ? (
            <form onSubmit={handleSubmit} noValidate>
              {/* Full Name Input */}
              <div className="form-group">
                <label>Full Name</label>
                <input
                  type="text"
                  name="fullName"
                  placeholder="Enter your full name"
                  value={formData.fullName}
                  onChange={handleChange}
                  required
                />
              </div>

              {/* Email Input */}
              <div className="form-group">
                <label>Email Address</label>
                <input
                  type="email"
                  name="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={handleChange}
                  style={errors.email ? { borderColor: '#ef4444', boxShadow: '0 0 0 1px #ef4444' } : {}}
                  required
                />
                {errors.email && (
                  <div style={{ display: 'flex', alignItems: 'center', color: '#ef4444', fontSize: '0.875rem', fontWeight: '500', marginTop: '0.5rem' }}>
                    <AlertTriangle size={16} style={{ marginRight: '0.5rem', flexShrink: 0 }} />
                    <span>{errors.email}</span>
                  </div>
                )}
              </div>

              {/* Mobile Number Input */}
              <div className="form-group">
                <label>Mobile Number</label>
                <div className="mobile-input-container">
                  <select
                    name="countryCode"
                    value={countryCode}
                    onChange={handleChange}
                    className="country-code-select"
                  >
                    {countryCodes.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <input
                    type="tel"
                    name="mobile"
                    className="mobile-number-input"
                    style={errors.mobile ? { borderColor: '#ef4444', boxShadow: '0 0 0 1px #ef4444' } : {}}
                    placeholder="Enter your 10-digit mobile number"
                    value={formData.mobile}
                    onChange={handleChange}
                    required
                    inputMode="numeric"
                    maxLength="10"
                  />
                </div>
                {errors.mobile && (
                  <div style={{ display: 'flex', alignItems: 'center', color: '#ef4444', fontSize: '0.875rem', fontWeight: '500', marginTop: '0.5rem' }}>
                    <AlertTriangle size={16} style={{ marginRight: '0.5rem', flexShrink: 0 }} />
                    <span>{errors.mobile}</span>
                  </div>
                )}
              </div>

              {/* Create Password Input */}
              <div className="form-group">
                <label>Create Password</label>
                <input
                  type="password"
                  name="password"
                  placeholder="Create a password"
                  value={formData.password}
                  onChange={handleChange}
                  style={errors.passwordMatch ? { borderColor: '#ef4444', boxShadow: '0 0 0 1px #ef4444' } : {}}
                  required
                />
              </div>

              {/* Confirm Password Input */}
              <div className="form-group">
                <label>Confirm Password</label>
                <input
                  type="password"
                  name="confirmPassword"
                  placeholder="Confirm your password"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  style={errors.passwordMatch ? { borderColor: '#ef4444', boxShadow: '0 0 0 1px #ef4444' } : {}}
                  required
                />
                {errors.passwordMatch && (
                  <div style={{ display: 'flex', alignItems: 'center', color: '#ef4444', fontSize: '0.875rem', fontWeight: '500', marginTop: '0.5rem' }}>
                    <AlertTriangle size={16} style={{ marginRight: '0.5rem', flexShrink: 0 }} />
                    <span>{errors.passwordMatch}</span>
                  </div>
                )}
              </div>

              {/* Agree to Terms Checkbox */}
              <div className="form-checkbox">
                <input
                  type="checkbox"
                  name="agreeTerms"
                  checked={formData.agreeTerms}
                  onChange={handleChange}
                  required
                />
                <span>
                  I agree to the{" "}
                  <a href="#" className="link">Terms of Service</a> and{" "}
                  <a href="#" className="link">Privacy Policy</a>
                </span>
              </div>

              <button type="submit" className="btn-primary">
                Create My Account
              </button>
            </form>
          ) : (
            <div className="otp-section">
              <h3>Enter the 4-digit OTP sent to your Email</h3>
              <div className="otp-inputs">
                {otp.map((digit, index) => (
                  <input
                    key={index}
                    type="text"
                    maxLength="1"
                    value={digit}
                    onChange={(e) => handleOtpChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(e, index)}
                    ref={(el) => (inputRefs.current[index] = el)}
                    className="otp-box"
                    inputMode="numeric"
                  />
                ))}
              </div>
              <button onClick={handleVerifyOTP} className="btn-primary mt-2">
                Verify OTP
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Info Container */}
      <div className="info-container">
        <div className="logo-container">
          <div className="logo">
            <img src="/logo.png" alt="AI Classroom Logo" height={30} width={30} />
            <span>AI Classroom</span>
          </div>
        </div>
        <div className="illustration">
          <img src="/images/computer-illustration.png" alt="Computer with learning tools" />
        </div>
        <div className="welcome-text">
          <h2>Welcome to AI Classroom!</h2>
          <p>Let's set up your personalized AI learning experience. It only takes 2 minutes!</p>
        </div>
      </div>
    </div>
  );
};

export default CreateAccount;