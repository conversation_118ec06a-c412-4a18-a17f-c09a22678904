
import { useState } from "react"
import "./sign.css"
import { useNavigate } from "react-router-dom"
import { useLocation } from "react-router-dom"
const EducationDetails = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { name, email, mobile, password } = location.state || {}
  const [showStream,setShowStream]=useState(true);
  const [formData, setFormData] = useState({
    board: "",
    language: "",
  })

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value,
    })
  }

  const handleBack = () => {
    // Navigate back to the previous page
    navigate(-1)
  }

  const handleContinue = () => {
    // Navigate to next page
    console.log("prvious details",{name:name,email:email,mobile:mobile,password:password})
    navigate("/student-type",{state:{name:name,email:email,mobile:mobile,password:password,board:formData.board,language:formData.language}})
  }

  return (
    <div className="container1" style={{ width: "100vw", minHeight: "100vh" }}>
      <div className="form-container" style={{ width: "100%", maxWidth: "600px" }}>
        <div className="form-content">
          <h1 className="form-title">Your Education Details</h1>
          <p className="form-subtitle">Tell us about your curriculum to personalize your content.</p>
          <div className="form-group mt-40">
            <label>Choose Your Stream</label>
            <select
              name="board"
              value={formData.board}
              onChange={handleChange}
              onClick={()=>{setShowStream(!showStream)}}
              className="input"
            >
              {showStream && (
              <option value="">Select Stream</option>
              )}
              <option value="Class 8 or below">Grade 8 or below</option>
              <option value="Class 12">Grade 12 or below</option>
              <option value="Diploma">Diploma</option>
              <option value="UG">Under Graduate</option>
              <option value="PG">Post Graduate</option>
              <option value="PhD">PHD</option>
              <option value="others">Others</option>
            </select>
            {formData.board === "others" && (
              <input
                type="text"
                name="customBoard"
                value={formData.customBoard || ""}
                onChange={e =>
                  setFormData({ ...formData, customBoard: e.target.value })
                }
                placeholder="Enter your stream"
                className="input mt-10"
                style={{ width: "100%" }}
              />
            )}
          </div>

          <div className="form-group mt-40">
            <label>Enter Your Institution Name</label>
            <input
              type="text"
              name="language"
              value={formData.language}
              onChange={handleChange}
              placeholder="Enter Name"
              className="input"
              style={{ width: "100%" }}
            />
          </div>

          <div className="button-group">
            <button className="btn-secondary" onClick={handleBack}>
              Back
            </button>
            <button className="btn-primary" onClick={handleContinue}>
              Continue
            </button>
          </div>
        </div>
      </div>

      <div className="info-container" style={{ width: "100%", maxWidth: "600px" }}>
        <div className="logo-container">
          <div className="logo">
            <img src="/logo.png" alt="AI Classroom Logo" height={30} width={30} />
            <span>AI Classroom</span>
          </div>
        </div>

        <div className="illustration">
          <img src="/images/education-illustration.png" alt="Education illustration" style={{ width: "100%" }} />
        </div>

        <div className="welcome-text">
          <h2>Welcome to AI Classroom!</h2>
          <p>Let's set up your personalized AI learning experience. It only takes 2 minutes!</p>
        </div>
      </div>
    </div>
  )
}

export default EducationDetails
