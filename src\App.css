@import "tailwindcss";

@keyframes progress {
  0% {
    width: 0%;
  }

  100% {
    width: 100%;
  }
}

.animate-progress {
  animation: progress 2s ease-in-out infinite;
}

@keyframes dotPulse {

  0%,
  100% {
    opacity: 0.2;
  }

  50% {
    opacity: 1;
  }
}

.dot {
  display: inline-block;
  animation: dotPulse 1.5s infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}


:root {
  --primary-color: #4070f4;
  --primary-light: #e6f0ff;
  --secondary-color: #a64bf4;
  --secondary-light: #f0e6ff;
  --accent-color: #f44b4b;
  --accent-light: #ffe6e6;
  --success-color: #4caf50;
  --success-light: #e8f5e9;
  --warning-color: #ff9800;
  --warning-light: #fff3e0;
  --text-primary: #333;
  --text-secondary: #666;
  --text-tertiary: #888;
  --bg-primary: #fff;
  --bg-secondary: #f5f7fb;
  --border-color: #e0e0e0;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
}

body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.app-container {
  display: flex;
  min-height: 100vh;
}

.content-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.primary-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 10px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primary-button:hover {
  background-color: #3060e0;
}

.secondary-button {
  background-color: #f0f0f0;
  color: var(--text-primary);
  border: none;
  border-radius: var(--radius-md);
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.secondary-button:hover {
  background-color: #e0e0e0;
}

.accent-button {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.accent-button:hover {
  background-color: #9540e0;
}