# Timer Component Documentation

## Overview
The `TimerComponent` is a circular timer designed for the AI Interview/Viva application. It displays elapsed time in HH:MM:SS format with a circular progress indicator and calls a callback function at specified intervals.

## Features
- **Circular Design**: Matches the Figma design with blue gradient text and circular progress
- **Auto-start**: Starts automatically when the interview begins
- **Interval Callbacks**: Calls a function every N minutes (configurable)
- **Visual Feedback**: Scales and changes opacity based on active state
- **Progress Animation**: True continuous clockwise rotation (never goes backward)
- **Auto-save**: Automatically saves interview progress every 5 minutes

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `isActive` | boolean | `false` | Controls whether the timer is running |
| `onIntervalCallback` | function | `null` | Function called every N minutes |
| `intervalMinutes` | number | `5` | Interval in minutes for callback |
| `onTimeUpdate` | function | `null` | Called on every time update |
| `resetTimer` | boolean | `false` | Resets timer when changed |

## Usage

### Basic Usage
```jsx
import TimerComponent from '../components/TimerComponent';

<TimerComponent 
    isActive={true}
    onIntervalCallback={(minutes, timeString) => {
        console.log(`${minutes} minutes elapsed: ${timeString}`);
    }}
    intervalMinutes={5}
/>
```

### In Viva.jsx
```jsx
// Timer state
const [timerActive, setTimerActive] = useState(false);

// Timer callback function
const handleTimerInterval = (totalMinutes, timeString) => {
    console.log(`Timer interval reached: ${totalMinutes} minutes (${timeString})`);
    
    // Auto-save functionality
    try {
        const chatData = {
            messages: messages,
            topic: topic,
            language: language,
            timestamp: new Date().toISOString(),
            duration: timeString
        };
        localStorage.setItem('vivaInterviewBackup', JSON.stringify(chatData));
        console.log('Interview progress auto-saved');
    } catch (error) {
        console.error('Failed to auto-save interview progress:', error);
    }
};

// Start timer when interview begins
const startSession = async () => {
    // ... existing code ...
    setTimerActive(true); // Start the timer
};

// In JSX
<TimerComponent 
    isActive={timerActive}
    onIntervalCallback={handleTimerInterval}
    intervalMinutes={5}
/>
```

## Styling
The component uses Tailwind CSS classes and matches the Figma design specifications:
- **Dimensions**: 179px × 179px
- **Text**: Blue gradient (#6C98FF to #7560FF)
- **Font**: Manrope, 34px, bold
- **Progress**: Blue stroke (#6F87FF) with glow effect (1-minute cycles)
- **Animation**: Smooth transitions and scaling

## Callback Function
The `onIntervalCallback` function receives two parameters:
1. `totalMinutes` (number): Total elapsed minutes
2. `timeString` (string): Formatted time string (HH:MM:SS)

## Auto-save Feature
The timer automatically saves interview progress to localStorage every 5 minutes with:
- Chat messages
- Topic and language settings
- Timestamp
- Current duration

## Integration Notes
1. **Start Timer**: Set `isActive={true}` when interview begins
2. **Stop Timer**: Set `isActive={false}` when interview ends or is reset
3. **Callback Interval**: Customize with `intervalMinutes` prop
4. **Visual Position**: Place in header section for visibility

## Example Implementation
The timer is integrated into the viva page header:
```jsx
<div className="flex items-center gap-8">
    <div className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 font-bold text-3xl md:text-4xl">
        AI Interview - LIVE
    </div>
    <TimerComponent 
        isActive={timerActive}
        onIntervalCallback={handleTimerInterval}
        intervalMinutes={5}
    />
</div>
```

## Customization
You can customize the timer by:
- Changing `intervalMinutes` for different callback frequencies
- Modifying the callback function for different auto-save behaviors
- Adjusting the visual styling in the component
- Adding additional props for more functionality
