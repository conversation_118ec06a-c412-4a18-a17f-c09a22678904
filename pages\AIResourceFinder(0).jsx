import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

// --- SVG Icon Components ---
const SearchIcon = ({ className }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const CloseIcon = ({ className }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);

const UploadIcon = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="45" height="45" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><path d="M12 13v8" /><path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242" /><path d="m8 17 4-4 4 4" /></svg>
);

const FileIcon = ({ className }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
  </svg>
);

const PencilIcon = ({ className }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10" />
  </svg>
);

const ArrowRightIcon = ({ className }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
  </svg>
);

const translator = (word1, word2) =>
  localStorage.getItem("lang") && localStorage.getItem("lang").toLowerCase().includes("english")
    ? word1
    : localStorage.getItem("lang")
      ? word2
      : word1;

export default function AIResourceFinder() {
  const [activeTab, setActiveTab] = useState('upload');
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [searchPrompt, setSearchPrompt] = useState('');
  const [specifications, setSpecifications] = useState('');
  const [manualEntryText, setManualEntryText] = useState(''); // New state for manual entry
  const [uploadedFile, setUploadedFile] = useState(null);
  const fileInputRef = useRef();
  const [pdfUrl, setPdfUrl] = useState(null);

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    setUploadedFile(file); // Optional: keep it if you want to store in state

    if (!file) return;

    const formData = new FormData();
    formData.append('pdf', file);

    try {
      const response = await axios.post(`${import.meta.env.VITE_API_AICLASSROOM}/pdfUpload/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('Upload success:', response.data);
      setPdfUrl(response.data.fileUrl); // Store the URL in state
    } catch (error) {
      console.error('Upload failed:', error);
    }
  };


  const navigate = useNavigate();

  const proceesWithData = () => {
    const dataToStore = {
      searchPrompt: searchPrompt + " " + specifications + " " + manualEntryText,
      // specifications: specifications,
      language: selectedLanguage,
      uploadedFileName: uploadedFile?.name || null,
      // manualEntryText: activeTab === 'manual' ? manualEntryText : null, // Store manual entry text
      pdfUrl: pdfUrl,
    };


    console.log("data to store from step1", dataToStore);

    localStorage.setItem("airf", JSON.stringify(dataToStore));
    navigate("/set-schedule");
  };

  return (
    <div className="bg-slate-100">
      <div className="w-full bg-purple-600 text-white p-6 shadow-lg w-screen"
        style={{
          background: 'linear-gradient(95.21deg, #A78BFA 0%, #818CF8 100%)',
        }}>
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold">
              {translator("AI Resource Finder", "एआई संसाधन खोजक")}
            </h1>
            <p className="text-sm opacity-90">
              {translator("Get your Resources all at one place", "अपने सभी संसाधन एक ही जगह पाएं")}
            </p>
          </div>
          <button className="p-1 hover:bg-white/20 rounded-full"
            onClick={() => navigate('/smart-resources')}
          >
            <CloseIcon className="w-6 h-6" />
          </button>
        </div>
      </div>

      <div className="bg-white p-6">
        <div>
          <label htmlFor="topic-search" className="text-lg font-semibold text-gray-800">
            {translator("Search About your Topic", "अपने विषय के बारे में खोजें")}
          </label>
          <div className="relative mt-2">
            <input
              id="topic-search"
              type="text"
              placeholder="Search Here"
              value={searchPrompt}
              onChange={(e) => setSearchPrompt(e.target.value)}
              className="w-full pl-4 pr-10 py-3 bg-gray-100 border border-gray-200 rounded-lg text-black focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition"
            />
            <SearchIcon className="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          </div>
        </div>

        <div className="flex items-center my-6">
          <hr className="flex-grow border-t border-gray-300" />
          <span className="px-4 text-gray-500 font-medium">
            {translator("OR", "या")}
          </span>
          <hr className="flex-grow border-t border-gray-300" />
        </div>

        <div className="flex space-x-8 border-b border-gray-200 mb-6">
          <button
            onClick={() => setActiveTab('upload')}
            className={`flex items-center space-x-2 pb-3 transition-all duration-300 ${activeTab === 'upload' ? 'border-b-2 border-purple-600 text-purple-600 font-semibold' : 'text-gray-500 hover:text-gray-800'}`}
          >
            <FileIcon className="w-5 h-5" />
            <span>{translator("Upload Syllabus", "सिलेबस अपलोड करें")}</span>
          </button>
          <button
            onClick={() => setActiveTab('manual')}
            className={`flex items-center space-x-2 pb-3 transition-all duration-300 ${activeTab === 'manual' ? 'border-b-2 border-purple-600 text-purple-600 font-semibold' : 'text-gray-500 hover:text-gray-800'}`}
          >
            <PencilIcon className="w-5 h-5" />
            <span>{translator("Manually Enter Topic Requirements", "मैन्युअल रूप से विषय आवश्यकताएँ दर्ज करें")}</span>
          </button>
        </div>

        {activeTab === 'upload' && (
          <div className="flex flex-col items-center justify-center w-full p-8 border-2 border-dashed border-[#A6C8FF] rounded-lg text-center">
            <UploadIcon className="w-20 h-20 text-gray-400 mb-3" />
            <p className="text-gray-600 mb-2">
              {translator("Drag & drop your syllabus file here", "यहाँ अपना सिलेबस फ़ाइल ड्रैग और ड्रॉप करें")}
            </p>
            <p className="text-gray-500 mb-4">or</p>
            <button
              className="bg-blue-500 text-white font-semibold px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors"
              style={{ background: 'linear-gradient(90deg, #6BA0FF 0%, #755BFF 100%)' }}
              onClick={() => fileInputRef.current.click()}
            >
              {translator("Browse Files", "फ़ाइलें ब्राउज़ करें")}
            </button>
            <input
              type="file"
              className="hidden"
              ref={fileInputRef}
              onChange={handleFileChange}
            />
            {uploadedFile && (
              <p className="mt-2 text-sm text-gray-700">{uploadedFile.name}</p>
            )}
          </div>
        )}

        {activeTab === 'manual' && (
          <div className="flex flex-col w-full p-8 border-2 border-dashed border-[#A6C8FF] rounded-lg">
            <label htmlFor="manual-entry" className="sr-only">
              {translator("Enter Topic Requirements Manually", "मैन्युअल रूप से विषय आवश्यकताएँ दर्ज करें")}
            </label>
            <textarea
              id="manual-entry"
              rows="6"
              placeholder={translator(
                "Enter your topic requirements here...",
                "अपनी विषय आवश्यकताएँ यहाँ दर्ज करें..."
              )}
              value={manualEntryText}
              onChange={(e) => setManualEntryText(e.target.value)}
              className="w-full p-3 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition resize-y"
            ></textarea>
          </div>
        )}

        <div className="mt-8 flex space-x-4">
          <div className="flex-1">
            <label htmlFor="language-select" className="text-lg font-semibold text-gray-800">
              {translator("Choose Your Preferred Language", "अपनी पसंदीदा भाषा चुनें")}{" "}
              <span className="text-gray-400 font-normal">
                {translator("(if any)", "(यदि कोई हो)")}
              </span>
            </label>
            <select
              id="language-select"
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value)}
              className="w-full mt-2 p-3 bg-white border border-gray-300 rounded-lg text-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition appearance-none pr-10"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd'%3E%3C/path%3E%3C/svg%3E")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'right 0.75rem center',
                backgroundSize: '1.5em 1.5em',
              }}
            >
              <option value="" disabled>{translator("Select Language", "भाषा चुनें")}</option>
              <option value="English">English</option>
              <option value="Hindi">Hindi</option>
              <option value="Hinglish">Hinglish</option>
            </select>
          </div>

          <div className="flex-[2]">
            <label htmlFor="specifications" className="text-lg font-semibold text-gray-800">
              {translator("Add Specifications", "विशेष विवरण जोड़ें")}{" "}
              <span className="text-gray-400 font-normal">
                {translator("(if any)", "(यदि कोई हो)")}
              </span>
            </label>
            <textarea
              id="specifications"
              rows="4"
              placeholder="Finish in 3 days, skip Unit 4, focus more on Calculus..."
              value={specifications}
              onChange={(e) => setSpecifications(e.target.value)}
              className="w-full mt-2 p-3 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition"
            ></textarea>
          </div>
        </div>

        <div className="mt-8 text-center">
          <button
            className="inline-flex items-center justify-center space-x-3 px-8 py-3 font-bold text-white bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg shadow-md hover:from-purple-600 hover:to-blue-600 transition-all transform hover:scale-105"
            onClick={proceesWithData}
          >
            <span>{translator("Generate Study Plan", "अध्ययन योजना बनाएं")}</span>
            <ArrowRightIcon className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
}