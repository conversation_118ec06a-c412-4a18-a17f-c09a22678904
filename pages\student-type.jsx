"use client"

import { useState } from "react"
import { useNavigate, useLocation } from "react-router-dom"
// You can keep this if other styles from sign.css are needed
import "./sign.css" 

const StudentType = () => {
  const router = useNavigate()
  const location = useLocation()
  
  // State to track the currently selected card
  const [selectedType, setSelectedType] = useState(null)

  const { name, email, mobile, password, board, language } = location.state || {}

  const handleBack = () => {
    router("/education-details")
  }

  // This function now handles setting the state and storing the value
  const handleCardClick = (type) => {
    // 1. Set the state to update the UI
    setSelectedType(type)
    
    // 2. Log to the console to confirm the click is working
    console.log("Selected type:", type)

    // 3. Store the value in localStorage for the 'Continue' button
    if (typeof window !== "undefined") {
      localStorage.setItem("studentType", type)
    }
  }

  const handlenext = async () => {
    // Read the type from localStorage
    const studentType = localStorage.getItem("studentType")

    // Ensure a type has been selected before proceeding
    if (!studentType) {
      alert("Please choose your purpose before continuing.")
      return
    }

    const payload = {
      email,
      firstName: name.split(" ")[0],
      lastName: name.split(" ")[1] || "", // Handle cases with no last name
      password,
      accounttype: studentType,
      signinwithgoogle: false,
    }

    try {
      const response = await fetch("https://api.aiclassroom.in/signup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })

      const data = await response.json()
      console.log("Signup response:", data)

      if (response.ok) {
        // Upon successful signup, immediately log in to get full user data and a fresh token
        setTimeout(() => {
          fetch("https://api.aiclassroom.in/login", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email, password }),
          })
            .then((loginResponse) => loginResponse.json())
            .then((loginData) => {
              if (loginData.token && loginData.user) {
                console.log("Login successful. User data:", loginData.user)
                
                // Store token and user data from the login response
                localStorage.setItem("auth-token", loginData.token)
                localStorage.setItem(
                  "user-data",
                  JSON.stringify({
                    userId: loginData.user._id,
                    email: loginData.user.email,
                    firstName: loginData.user.firstName,
                    avatar: loginData.user.avatar,
                    coins: loginData.user.coins,
                    totalCoins: loginData.user.totalCoins,
                  })
                )
                
                // Navigate to the dashboard
                router("/dashboard", {
                  state: { name, email, mobile, password, board, language, studentType },
                })
              } else {
                alert(loginData.message || "An error occurred during login after signup.")
              }
            })
            .catch(error => {
                console.error("Error during login API call:", error)
                alert("An error occurred during the login process. Please try again.")
            })
        }, 500) // Reduced delay slightly
      } else {
        alert(data.message || "An error occurred during sign up.")
      }
    } catch (error) {
      console.error("Error during sign up process:", error)
      alert("A network error occurred. Please try again.")
    }
  }

  return (
    <>
      {/* CSS STYLES EMBEDDED HERE FOR RELIABILITY */}
      <style>{`
        .student-type-card {
          cursor: pointer; /* Indicates the element is clickable */
          border: 2px solid transparent; /* Start with a transparent border to prevent layout shift */
          transition: transform 0.2s ease-in-out, border-color 0.2s ease-in-out;
        }

        .student-type-card.selected {
          border-color: #9400D3; /* Blue border when selected */
          transform: scale(0.95); /* Makes the card slightly smaller */
          box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
        }
      `}</style>
      
      <div className="container1">
        <div className="form-container">
          <div className="form-content">
            <h1 className="form-title">Choose your Purpose !</h1>
            <p className="form-subtitle">
              Help us personalize your learning experience.
            </p>

            <div className="student-type-grid">
              {/* Each card now checks against the 'selectedType' state */}
              <div
                className={`student-type-card ${selectedType === "coaching" ? "selected" : ""}`}
                onClick={() => handleCardClick("coaching")}
              >
                <div className="icon-circle">
                  <div className="icon coaching-icon">
                    <img src="/coaching.png" alt="Coaching Icon" />
                  </div>
                </div>
                <span>Study Help</span>
              </div>

              <div
                className={`student-type-card ${selectedType === "Course Assistance" ? "selected" : ""}`}
                onClick={() => handleCardClick("Course Assistance")}
              >
                <div className="icon-circle">
                  <div className="icon school-icon">
                    <img src="/school.png" alt="Course Assistance Icon" />
                  </div>
                </div>
                <span>Course Assistance</span>
              </div>

              <div
                className={`student-type-card ${selectedType === "Independent Learning" ? "selected" : ""}`}
                onClick={() => handleCardClick("Independent Learning")}
              >
                <div className="icon-circle">
                  <div className="icon independent-icon">
                    <img src="/independent.png" alt="Independent Learning Icon" />
                  </div>
                </div>
                <span>Independent Learning</span>
              </div>

              <div
                className={`student-type-card ${selectedType === "Exam Preparation" ? "selected" : ""}`}
                onClick={() => handleCardClick("Exam Preparation")}
              >
                <div className="icon-circle">
                  <div className="icon college-icon">
                    <img src="/college.png" alt="Exam Preparation Icon" />
                  </div>
                </div>
                <span>Exam Preparation</span>
              </div>
            </div>

            <div className="button-group">
              <button className="btn-secondary" onClick={handleBack}>
                Back
              </button>
              <button className="btn-primary" onClick={handlenext}>
                Continue
              </button>
            </div>
          </div>
        </div>

        <div className="info-container">
          <div className="logo-container">
            <div className="logo">
              <img src="/logo.png" alt="AI Classroom Logo" height={30} width={30} />
              <span>AI Classroom</span>
            </div>
          </div>
          <div className="illustration">
            <img src="/images/student-illustration.png" alt="Students learning" />
          </div>
          <div className="welcome-text">
            <h2>Welcome to AI Classroom!</h2>
            <p>
              Let's set up your personalized AI learning experience. It only takes 2 minutes!
            </p>
          </div>
        </div>
      </div>
    </>
  )
}

export default StudentType