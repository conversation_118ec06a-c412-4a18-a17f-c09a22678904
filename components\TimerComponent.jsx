import React, { useState, useEffect, useRef } from 'react';

const TimerComponent = ({
    isActive = false,
    onIntervalCallback = null,
    intervalMinutes = 5,
    onTimeUpdate = null,
    resetTimer = false
}) => {
    const [seconds, setSeconds] = useState(0);
    const [minutes, setMinutes] = useState(0);
    const [hours, setHours] = useState(0);
    const [totalElapsedSeconds, setTotalElapsedSeconds] = useState(0);
    const intervalRef = useRef(null);
    const lastCallbackRef = useRef(0);

    // Reset timer when resetTimer prop changes
    useEffect(() => {
        if (resetTimer) {
            setSeconds(0);
            setMinutes(0);
            setHours(0);
            setTotalElapsedSeconds(0);
            lastCallbackRef.current = 0;
        }
    }, [resetTimer]);

    // Format time to HH:MM:SS
    const formatTime = (h, m, s) => {
        return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
    };

    // Calculate total seconds for progress calculation
    const getTotalSeconds = () => {
        return hours * 3600 + minutes * 60 + seconds;
    };

    // Calculate progress for circular indicator (0-360 degrees)
    const getProgress = () => {
        // Complete circle every 1 minute (60 seconds)
        // Use totalElapsedSeconds for continuous rotation without visual reset
        const progressInCycle = (totalElapsedSeconds % 60) / 60;
        return progressInCycle * 360;
    };

    useEffect(() => {
        if (isActive) {
            intervalRef.current = setInterval(() => {
                // Increment continuous counter for smooth progress
                setTotalElapsedSeconds(prev => prev + 1);

                // Update display time
                setSeconds(prevSeconds => {
                    const newSeconds = prevSeconds + 1;

                    if (newSeconds === 60) {
                        setMinutes(prevMinutes => {
                            const newMinutes = prevMinutes + 1;

                            if (newMinutes === 60) {
                                setHours(prevHours => prevHours + 1);
                                return 0;
                            }
                            return newMinutes;
                        });
                        return 0;
                    }
                    return newSeconds;
                });
            }, 1000);
        } else {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
        }

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [isActive]);

    // Handle interval callback
    useEffect(() => {
        const totalMinutes = Math.floor(getTotalSeconds() / 60);

        if (totalMinutes > 0 &&
            totalMinutes % intervalMinutes === 0 &&
            totalMinutes !== lastCallbackRef.current &&
            onIntervalCallback &&
            isActive) {

            lastCallbackRef.current = totalMinutes;
            onIntervalCallback(totalMinutes, formatTime(hours, minutes, seconds));
        }
    }, [seconds, minutes, hours, intervalMinutes, onIntervalCallback, isActive]);

    // Call onTimeUpdate when time changes
    useEffect(() => {
        if (onTimeUpdate) {
            onTimeUpdate(formatTime(hours, minutes, seconds), getTotalSeconds());
        }
    }, [seconds, minutes, hours, onTimeUpdate]);

    const progress = getProgress();
    const circumference = 2 * Math.PI * 85; // radius of 85
    const strokeDashoffset = circumference - (progress / 360) * circumference;

    return (
        <div className="relative flex items-center justify-center">
            {/* Outer container matching Figma dimensions */}
            <div className={`relative w-[179px] h-[179px] flex items-center justify-center transition-transform duration-300 ${isActive ? 'scale-100' : 'scale-95 opacity-75'}`}>
                {/* Background circle with white stroke */}
                <svg
                    className="absolute inset-0 w-full h-full transform -rotate-90"
                    viewBox="0 0 178.86 178.86"
                >
                    {/* Background circle */}
                    <circle
                        cx="89.43"
                        cy="89.43"
                        r="85.43"
                        fill="transparent"
                        stroke="#FFFFFF"
                        strokeWidth="8"
                        style={{
                            filter: 'inset 0px 0px 3.74px 0px rgba(211, 211, 211, 1)'
                        }}
                    />

                    {/* Inner background circle */}
                    <circle
                        cx="89.43"
                        cy="89.43"
                        r="85.75"
                        fill="transparent"
                        stroke="#D3D3D3"
                        strokeWidth="0.53"
                    />

                    {/* Progress circle */}
                    <circle
                        cx="89.43"
                        cy="89.43"
                        r="85"
                        fill="transparent"
                        stroke="#6F87FF"
                        strokeWidth="1.07"
                        strokeDasharray={circumference}
                        strokeDashoffset={strokeDashoffset}
                        strokeLinecap="butt"
                        style={{
                            filter: 'drop-shadow(0px 0px 2.14px rgba(0, 194, 255, 1))'
                        }}
                    />
                </svg>

                {/* Timer text with gradient */}
                <div className="absolute inset-0 flex items-center justify-center">
                    <span
                        className="font-bold text-[34px] leading-[1.37] tracking-[0.01em]"
                        style={{
                            background: 'linear-gradient(180deg, #6C98FF 0%, #7560FF 100%)',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            backgroundClip: 'text',
                            textShadow: '0px 1.07px 2.14px rgba(0, 0, 0, 0.16)',
                            fontFamily: 'Manrope, sans-serif'
                        }}
                    >
                        {formatTime(hours, minutes, seconds)}
                    </span>
                </div>


            </div>
        </div>
    );
};

export default TimerComponent;
