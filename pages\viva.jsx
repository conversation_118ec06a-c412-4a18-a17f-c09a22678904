import React, { useState, useRef, useEffect } from 'react';
import { MdOutlineDownloadForOffline } from "react-icons/md";
import { HiRefresh } from "react-icons/hi";
import { IoCloseCircleOutline } from "react-icons/io5";
import { FaMicrophone } from "react-icons/fa";
import { IoIosVideocam } from "react-icons/io";
import { IoSend } from "react-icons/io5";
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import PreferenceModal from './modals/PreferenceModal';
import TimerComponent from '../components/TimerComponent';
import { createAvatarSynthesizer, createWebRTCConnection } from "./Utility";
import { avatarAppConfig } from "./config";
import axios from 'axios';
import * as SpeechSDK from "microsoft-cognitiveservices-speech-sdk";
import { FaUserCircle } from "react-icons/fa";
import { PiUserFocusFill } from "react-icons/pi";
import { jsPDF } from "jspdf";
import logoBase64 from './logobase64';
import { delay } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

/* ────────────────────────────────────────────────────────────────────────── */
/*  🔈 1.  PUT YOUR SPEECH KEY & REGION IN ENV VARIABLES (recommended)       */
/* ------------------------------------------------------------------------ */
const SPEECH_KEY = import.meta.env.VITE_AZURE_SPEECH_KEY;      // or a hard-coded test key
const SPEECH_REGION = import.meta.env.VITE_AZURE_SPEECH_REGION; // e.g. "eastus"


const Viva = () => {
    // Chat state
    const [isMobile, setIsMobile] = useState(false);
    const navigate = useNavigate();
    const [avatarSynthesizer, setAvatarSynthesizer] = useState(null);
    const [interviewStarted, setInterviewStarted] = useState(false);
    const [language, setLanguage] = useState("hindi");
    const [topic, setTopic] = useState("General Knowledge");
    const [input, setInput] = useState('');
    const [initialMessagesDone, setInitialMessagesDone] = useState(false);
    const [messages, setMessages] = useState([
        {
            from: 'ai',
            text: "Hey! No worries—we can turn this around. First, tell me: what topics are covered in the exam?",
        },
        {
            from: 'user',
            text: "Viva",
        },
    ]);


    // Responsive detection
    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < window.innerHeight);
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);

        return () => {
            window.removeEventListener('resize', checkMobile);
        };
    }, []);


    //popup

    useEffect(() => {
        const handleBeforeUnload = (e) => {
            if (interviewStarted) {
                // Clear all session data
                localStorage.removeItem('userVivaPreference');
                localStorage.removeItem('vivaInterviewBackup');

                // Stop any ongoing processes
                stopAzureMic();
                if (avatarSynthesizer) {
                    avatarSynthesizer.close();
                }
                if (streamRef.current) {
                    streamRef.current.getTracks().forEach(track => track.stop());
                }

                // Trigger browser's default warning
                e.preventDefault();
                e.returnValue = '';
                return '';
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [interviewStarted, avatarSynthesizer]);

    // refresh/close confirmation
    useEffect(() => {
        const handleUnload = () => {
            if (interviewStarted) {
                // Clear all state-related data
                localStorage.removeItem('userVivaPreference');
                localStorage.removeItem('vivaInterviewBackup');
            }
        };

        window.addEventListener('unload', handleUnload);

        return () => {
            window.removeEventListener('unload', handleUnload);
        };
    }, [interviewStarted]);

    // cleanuppp
    useEffect(() => {
        return () => {

            stopAzureMic();
            if (avatarSynthesizer) {
                avatarSynthesizer.close();
            }
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => track.stop());
            }
        };
    }, [avatarSynthesizer]);



    //UPDATED 
    useEffect(() => {
        const stored = JSON.parse(localStorage.getItem('userVivaPreference'));
        if (stored) {
            setTopic(stored.topic);
            setLanguage(stored.language);
        }
    }, []);

    // Webcam state
    const [webcamOn, setWebcamOn] = useState(false);
    const videoRef = useRef(null);
    const streamRef = useRef(null);
    const avatarVideoRef = useRef();
    const avatarAudioRef = useRef();

    const speakText = (text) => {
        return new Promise((resolve, reject) => {
            if (!avatarSynthesizer) return reject("Avatar not ready.");
            avatarSynthesizer.speakTextAsync(
                text,
                (result) => {
                    if (result.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted) resolve();
                    else reject("Synthesis failed.");
                },
                (error) => reject(error)
            );
        });
    };

    const handleOnTrack = (event) => {
        if (event.track.kind === "video") {
            avatarVideoRef.current.srcObject = event.streams[0];
        } else if (event.track.kind === "audio") {
            avatarAudioRef.current.srcObject = event.streams[0];
            avatarAudioRef.current.muted = false;
            avatarAudioRef.current.play().catch(() => { });
        }
    };

    const startSession = async () => {
        console.log("Starting session...");
        // const allowed = await requestMicAccess();
        // if (!allowed) return;
        // await startUserCamera();

        const pc = createWebRTCConnection(
            avatarAppConfig.iceUrl,
            avatarAppConfig.iceUsername,
            avatarAppConfig.iceCredential
        );
        pc.ontrack = handleOnTrack;
        pc.addTransceiver("video", { direction: "sendrecv" });
        pc.addTransceiver("audio", { direction: "sendrecv" });

        const avatar = createAvatarSynthesizer({
            voice: language === "hindi" ? "hi-IN-SwaraNeural" : "en-IN-NeerjaNeural",
        });
        await avatar.startAvatarAsync(pc);
        setAvatarSynthesizer(avatar);
        setInterviewStarted(true);
        setTimerActive(true); // Start the timer when interview begins

        // Automatically speak greeting message when avatar is ready
        try {
            await avatar.speakTextAsync(
                "Hello! I am your interviewer Neerja. This is an Ai Interview session. Shall we begin?",
                (result) => {
                    if (result.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted) {
                        console.log("Greeting message spoken successfully");
                    } else {
                        console.log("Greeting synthesis failed:", result);
                    }
                },
                (error) => {
                    console.error("Error speaking greeting:", error);
                }
            );
        } catch (error) {
            console.error("Error with automatic greeting:", error);
        }

        // setStatus("Interview started.");
    };

    // Speech recognition state
    const {
        transcript,
        listening,
        resetTranscript,
        browserSupportsSpeechRecognition
    } = useSpeechRecognition();
    const [micActive, setMicActive] = useState(false);

    // Modal state
    const [showPreferenceModal, setShowPreferenceModal] = useState(true);
    const [userPreference, setUserPreference] = useState(null);
    const [initialPref, setInitialPref] = useState({ topic: '', language: '' });
    const [showResetModal, setShowResetModal] = useState(false);

    // Timer state
    const [timerActive, setTimerActive] = useState(false);

    // Timer callback function - called every 5 minutes
    const handleTimerInterval = (totalMinutes, timeString) => {
        console.log(`Timer interval reached: ${totalMinutes} minutes (${timeString})`);

        // You can implement any functionality here that should happen every 5 minutes
        // For example:
        // - Save interview progress
        // - Send analytics data
        // - Show a notification
        // - Auto-save chat history

        // Example: Auto-save chat to localStorage every 5 minutes
        // try {
        //     const chatData = {
        //         messages: messages,
        //         topic: topic,
        //         language: language,
        //         timestamp: new Date().toISOString(),
        //         duration: timeString
        //     };
        //     // localStorage.setItem('vivaInterviewBackup', JSON.stringify(chatData));
        //     // console.log('Interview progress auto-saved');
        // } catch (error) {
        //     console.error('Failed to auto-save interview progress:', error);
        // }
    };


    const askOpenAi = async (input) => {
        console.log("chat input input", input);
        //setMessages([...messages, { from: 'user', text: input.toString() }]);
        setMessages(prev => [...prev, { from: 'user', text: input }]);
        try {
            const response = await axios.post(
                'https://api.openai.com/v1/chat/completions',
                {
                    model: 'gpt-3.5-turbo',
                    messages: [
                        {
                            role: "system",
                            content: `Given the Topic "${topic}".
                                    You are an AI Interviewer conducting a realistic interview. Your role is to evaluate the user's knowledge and responses, only providing answers if the user explicitly asks for them.
                                    Engaging in the Interview: Start with a simple question relevant to the user's field to ease them into the interview. For example:
                                    If they are in technology: "Can you briefly explain what a computer is?"
                                    If they are in medicine: "Can you tell me what a stethoscope is used for?"
                                    If they are in finance: "What is the purpose of a bank?"
                                    Adapt the question accordingly based on their field.
                                    Guidelines:
                                    Do not provide answers unless the user directly asks.
                                    Encourage the user to elaborate on their responses.
                                    Offer feedback or hints only if the user seems uncertain.
                                    Don't ask like based on the Data you provided.
                                    Don't comment on the Topic.
                                    Ask one question at a time about "${topic}". User's last answer is: ${messages[messages.length - 1]?.text || "none"}`
                        },
                        { role: "user", content: input },
                    ],
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`
                    }
                }
            );

            console.log("response from ai", response);

            const reply = response.data.choices[0].message.content;
            console.log("reply", reply);
            //setMessages([...messages, { from: 'ai', text: reply }]);
            setMessages(prev => [...prev, { from: 'ai', text: reply }]);
            await speakText(reply);
            return reply;
        } catch (err) {
            console.log("error in fetching", err.response?.data || err.message || err);
        }
    };

    useEffect(() => {
        console.log('messages--', messages);
    }, [messages]);


    // On mount, check localStorage for userVivaPreference
    useEffect(() => {
        const stored = localStorage.getItem('userVivaPreference');
        if (stored) {
            try {
                const parsed = JSON.parse(stored);
                setInitialPref({
                    topic: parsed.topic || '',
                    language: parsed.language || ''
                });
                setUserPreference(parsed);
            } catch {
                // ignore parse error
            }
        }
        setShowPreferenceModal(true); // Always show modal on load
    }, []);

    // Webcam logic
    useEffect(() => {
        if (webcamOn) {
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(stream => {
                    if (videoRef.current) {
                        videoRef.current.srcObject = stream;
                        streamRef.current = stream;
                    }
                })
                .catch(() => {
                    setWebcamOn(false);
                });
        } else {
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => track.stop());
                streamRef.current = null;
            }
            if (videoRef.current) {
                videoRef.current.srcObject = null;
            }
        }
        return () => {
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => track.stop());
                streamRef.current = null;
            }
        };
    }, [webcamOn]);

    // Mic logic
    // useEffect(() => {
    //     if (micActive && browserSupportsSpeechRecognition) {
    //         resetTranscript();
    //         SpeechRecognition.startListening({ continuous: true, language: 'en-IN' });
    //     } else {
    //         SpeechRecognition.stopListening();
    //     }
    //     // When mic is stopped, update input with transcript
    //     if (!micActive && transcript) {
    //         setInput(transcript);
    //     }
    //     // eslint-disable-next-line
    // }, [micActive]);

    // Update input live while listening
    useEffect(() => {
        if (micActive && listening) {
            setInput(transcript);
        }
        // eslint-disable-next-line
    }, [transcript, listening]);

    // Send message
    const handleSend = async () => {
        if (!input.trim()) return;
        // setMessages([...messages, { from: 'user', text: input }]);
        askOpenAi(input);
        setInput('');
    };

    // Toggle webcam
    const handleToggleWebcam = () => {
        setWebcamOn(prev => !prev);
    };

    // Toggle mic
    const handleToggleMic = () => {
        if (!browserSupportsSpeechRecognition) {
            alert('Your browser does not support speech recognition.');
            return;
        }
        setMicActive(prev => !prev);
    };
    const recognizerRef = useRef(null);
    const autoSendTimeoutRef = useRef(null);

    /* ───── 3. Azure Speech Recognition logic ───────────────────────────── */
    const startAzureMic = () => {
        /* configure recognizer */
        const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
            SPEECH_KEY,
            SPEECH_REGION
        );
        speechConfig.speechRecognitionLanguage = "en-IN";

        const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
        const recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);

        recognizer.recognizing = (_, e) => {
            /* live stream while speaking */
            setInput(e.result.text);

            // Clear any existing timeout when user is actively speaking
            if (autoSendTimeoutRef.current) {
                clearTimeout(autoSendTimeoutRef.current);
                autoSendTimeoutRef.current = null;
            }
        };

        recognizer.recognized = (_, e) => {
            if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech) {
                const finalText = e.result.text.trim();
                setInput(finalText);

                // Set timeout to auto-send after 1 second of silence
                if (finalText) {
                    autoSendTimeoutRef.current = setTimeout(() => {
                        // Auto-send the recognized text
                        askOpenAi(finalText);
                        setInput(''); // Clear input after sending
                        autoSendTimeoutRef.current = null;
                        setMicActive(true);
                    }, 5000); // delay
                }
            }
        };

        recognizer.canceled = (_, e) => {
            console.error("Recognition canceled:", e);
            recognizer.stopContinuousRecognitionAsync();
            setMicActive(false);

            // Clear timeout on cancel
            if (autoSendTimeoutRef.current) {
                clearTimeout(autoSendTimeoutRef.current);
                autoSendTimeoutRef.current = null;
            }
        };

        recognizer.sessionStopped = () => {
            recognizer.stopContinuousRecognitionAsync();
            setMicActive(false);

            // Clear timeout on session stop
            if (autoSendTimeoutRef.current) {
                clearTimeout(autoSendTimeoutRef.current);
                autoSendTimeoutRef.current = null;
            }
        };

        recognizer.startContinuousRecognitionAsync();
        recognizerRef.current = recognizer;
    };

    const stopAzureMic = () => {
        recognizerRef.current?.stopContinuousRecognitionAsync(() => {
            recognizerRef.current?.close();
            recognizerRef.current = null;
        });

        // Clear any pending auto-send timeout when stopping mic
        if (autoSendTimeoutRef.current) {
            clearTimeout(autoSendTimeoutRef.current);
            autoSendTimeoutRef.current = null;
        }
    };



    /* toggle mic */
    useEffect(() => {
        if (micActive) startAzureMic();
        else stopAzureMic();
        // cleanup on unmount
        return stopAzureMic;
    }, [micActive]);

    // Handle reset quiz
    const handleResetQuiz = () => {
        setShowResetModal(true);
    };

    const handleConfirmReset = () => {
        // Stop all ongoing processes
        setTimerActive(false);
        stopAzureMic();

        if (avatarSynthesizer) {
            avatarSynthesizer.close();
        }

        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
        }

        // Clear all state
        setAvatarSynthesizer(null);
        setInterviewStarted(false);
        setLanguage("hindi");
        setTopic("General Knowledge");
        setInput('');
        setInitialMessagesDone(false);
        setMessages([
            {
                from: 'ai',
                text: "Hey! No worries—we can turn this around. First, tell me: what topics are covered in the exam?",
            },
            {
                from: 'user',
                text: "Viva",
            },
        ]);

        // Clear storage
        localStorage.removeItem('userVivaPreference');
        localStorage.removeItem('vivaInterviewBackup');

        setShowResetModal(false);
        setShowPreferenceModal(true);
    };

    // Function to trim messages to keep only last 2000 characters
    const getTrimmedMessages = () => {
        // Convert all messages to a single string to calculate total length
        let totalLength = 0;
        let trimmedMessages = [];

        // Start from the end and work backwards to keep the most recent messages
        for (let i = messages.length - 1; i >= 0; i--) {
            const message = messages[i];
            const messageText = message.text || '';

            // Check if adding this message would exceed 2000 characters
            if (totalLength + messageText.length <= 2000) {
                // Add the full message
                trimmedMessages.unshift(message);
                totalLength += messageText.length;
            } else {
                // Calculate how many characters we can take from this message
                const remainingChars = 2000 - totalLength;

                if (remainingChars > 0) {
                    // Take only the last part of this message to reach exactly 2000 chars
                    const trimmedText = messageText.slice(-remainingChars);
                    trimmedMessages.unshift({
                        ...message,
                        text: trimmedText
                    });
                }
                break; // Stop processing older messages
            }
        }

        return trimmedMessages;
    };

    // Example function to demonstrate usage of getTrimmedMessages
    const processMessagesForAPI = async () => {
        const trimmedMessages = JSON.stringify(getTrimmedMessages());
        console.log('Original messages count:', messages.length);
        console.log('Trimmed messages count:', trimmedMessages.length);

        // Calculate total characters in trimmed messages
        // const totalChars = trimmedMessages.reduce((total, msg) => total + (msg.text?.length || 0), 0);
        // console.log('Total characters in trimmed messages:', totalChars);
        console.log('trimed message--- ', trimmedMessages)

        // You can now pass trimmedMessages to another function
        // Example: sendToAnotherFunction(trimmedMessages);

        try {
            const response = await axios.post(
                'https://api.openai.com/v1/chat/completions',
                {
                    model: 'gpt-3.5-turbo',
                    messages: [
                        {
                            role: "system",
                            content: `You are an expert evaluator and mentor, experienced in assessing student or candidate interviews, vivas, or technical discussions.
                                    I will provide the full transcript of my interaction (a viva/interview) with an AI system ChatGPT. Please analyze the entire conversation deeply and provide:
                                    1. **Overall Performance Summary**:
                                    - How well did I perform?
                                    - Was I confident, logical, and consistent?
                                    2. **Knowledge & Understanding**:
                                    - What subjects/topics did I demonstrate strong understanding of?
                                    - Where did my explanations or understanding seem weak or unclear?
                                    3. **Strengths Identified**:
                                    - Any notable strengths in reasoning, technical skills, problem-solving, or articulation?
                                    4. **Weaknesses / Gaps**:
                                    - List any recurring mistakes, misconceptions, or areas where I lacked depth or precision.
                                    5. **Improvement Suggestions**:
                                    - How can I improve my knowledge, answer quality, or overall performance in future interviews?
                                    - Recommend learning resources, habits, or strategies if relevant.
                                    6. **Communication and Clarity**:
                                    - Was I articulate and clear?
                                    - Were my questions well-formed and purposeful?
                                    7. **Final Rating (Optional)**:
                                    - Provide a score or rating if applicable (e.g., 7.5/10, Good, Average, Excellent).
                                    Make the tone of feedback constructive, professional, and supportive — like a mentor guiding me toward excellence.
                                    you will be provided wuth the viva or interview chat transcript example format-
                                    {
                                        from: 'ai',
                                        text: "What is a data structure?",
                                    },
                                    {
                                        from: 'user',
                                        text: " A data structure is a way of organizing and storing data in a computer so that it can be accessed and used efficiently. Examples include arrays, linked lists, trees, and graphs. ",
                                    }
                                    now you will analise all the chat and give the unbaised feedback in one paragraph and no markdown charecter
                                    `
                        },
                        { role: "user", content: `Give me my feedback based on the transcript. Here is the full conversation transcript: ${trimmedMessages}` },
                    ],
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`
                    }
                }
            );

            console.log("response from ai", response);

            const reply = response.data.choices[0].message.content;
            console.log("reply from ai", reply);
            return reply;

        } catch (err) {
            console.log("error in fetching", err.response?.data || err.message || err);
        }

        // return trimmedMessages;
    };

    const downloadChatAsPDF = async () => {
        const feedback = await processMessagesForAPI();
        const doc = new jsPDF({ unit: 'pt' }); // Use points for units

        const pageWidth = doc.internal.pageSize.getWidth(); // Width of the page in points
        const margin = 36; // Margin in points (approx 0.5 inch as 1 inch = 72 points)
        const topDocMargin = 50; // Increased top margin to accommodate logo
        let yPosition = topDocMargin; // Initial Y position for drawing
        const contentMaxWidth = pageWidth - (2 * margin); // Max width for text content within margins

        // Get current date and time in a nicely formatted string
        const now = new Date();
        const downloadDateTime = now.toLocaleDateString('en-GB') + ' ' +
            now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

        // --- Header Section ---
        // "Topic Tile Fetched" - larger and bold
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(16);
        const lessonTitle = topic || "Chat Topic";
        doc.text(`${lessonTitle}`, margin, 25);

        // Logo and AI Classroom text at top right
        try {
            // Ensure logoBase64 has proper data URL format
            let logoData = logoBase64;
            if (!logoData.startsWith('data:')) {
                // If logoBase64 doesn't have data URL prefix, add it
                logoData = `data:image/png;base64,${logoBase64}`;
            }

            const logoWidth = 130;
            const logoHeight = 36; // Kept original height
            const logoX = pageWidth - margin - logoWidth - 20;
            doc.addImage(logoData, 'PNG', logoX, 15, logoWidth, logoHeight);
        } catch (error) {
            console.warn('Could not add logo to PDF:', error);
        }


        yPosition += 18 * 1.2; // Current yPosition = 50 + 21.6 = 71.6

        // Date and time of download
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);
        doc.text(` ${downloadDateTime}`, margin, 45); // Fixed position for date

        // Space after the entire header block before chat content begins
        yPosition = 70; // A fixed start after the header elements

        // --- Chat Messages Section ---
        const chatFontSize = 10;
        const originalChatLineHeight = chatFontSize * 3;

        const splitTextIntoLines = (text, maxWidthForSplit) => {
            return doc.splitTextToSize(text, maxWidthForSplit);
        };

        // Fixed: Use your actual messages array and correct property names
        const messagesSource = messages;

        // Fixed: Filter messages correctly - skip first two messages (AI greeting and "Viva" response)
        const messagesToDisplay = messagesSource.filter((message, index) => {
            return index > 1 && message.text && message.text.trim() !== '';
        });

        const boxPadding = 8; //
        const textLineHeightInBox = chatFontSize * 2; //

        for (let i = 0; i < messagesToDisplay.length; i++) {
            let message = messagesToDisplay[i];
            let nextMessage = (i + 1 < messagesToDisplay.length) ? messagesToDisplay[i + 1] : null;

            doc.setFont('helvetica', 'normal'); // Reset default font for each block
            doc.setFontSize(chatFontSize);

            if (message.from === 'user' && nextMessage && nextMessage.from === 'ai') {
                const prefixUser = 'Me : ';
                // Fixed: Use 'text' property instead of 'content'
                const userContent = prefixUser + message.text;

                const userLines = splitTextIntoLines(userContent, contentMaxWidth - (2 * boxPadding));

                const prefixAI = 'AI : ';
                // Fixed: Use 'text' property instead of 'content'
                const aiContent = prefixAI + nextMessage.text;
                const aiLines = splitTextIntoLines(aiContent, contentMaxWidth - (2 * boxPadding));

                const userTextHeight = userLines.length * textLineHeightInBox;
                const aiTextHeight = aiLines.length * textLineHeightInBox;

                let spaceBetweenUserAndAIInBox = 0;
                if (userLines.length > 0 && aiLines.length > 0) {
                    spaceBetweenUserAndAIInBox = textLineHeightInBox * 0.6; // Gap between Me and AI text within the box
                }

                const contentHeightInBox = userTextHeight + spaceBetweenUserAndAIInBox + aiTextHeight;
                const boxHeight = contentHeightInBox + (3 * boxPadding); // Total height of the box

                // Page break check for the entire box
                if (yPosition + boxHeight > doc.internal.pageSize.getHeight() - topDocMargin) {
                    doc.addPage();
                    yPosition = topDocMargin;
                }

                // Draw the box
                doc.setDrawColor(200, 200, 200); // Light grey border for the box
                doc.setFillColor(248, 248, 248); // Very light grey fill for the box
                doc.rect(margin, yPosition, contentMaxWidth, boxHeight, 'FD'); // Fill and Draw

                // Render text inside the box
                // Initial baseline for the first line of text inside the box
                let yTextInBox = yPosition + boxPadding + chatFontSize;

                userLines.forEach(line => {
                    doc.text(line, margin + boxPadding, yTextInBox);
                    yTextInBox += textLineHeightInBox;
                });

                // If there was user text and there will be AI text, add the specific gap
                if (userLines.length > 0 && aiLines.length > 0) {
                    yTextInBox += spaceBetweenUserAndAIInBox;
                }

                aiLines.forEach(line => {
                    doc.text(line, margin + boxPadding, yTextInBox);
                    yTextInBox += textLineHeightInBox;
                });

                yPosition += boxHeight + (textLineHeightInBox * 0.75); // Move yPosition down by box height + some margin after the box

                i++; // Increment i because we've processed the nextMessage as well
            } else {
                // --- ORIGINAL MESSAGE RENDERING (SINGLE MESSAGE) ---
                doc.setFont('helvetica', 'normal');
                doc.setFontSize(chatFontSize);

                const prefix = message.from === 'user' ? 'Me : ' : 'AI : ';
                // Fixed: Use 'text' property instead of 'content'
                const fullTextMessage = prefix + message.text;
                const textLines = splitTextIntoLines(fullTextMessage, contentMaxWidth); // Use full contentMaxWidth

                // Original block-level page break estimation
                const originalBlockEstimatedHeight = textLines.length * originalChatLineHeight;
                const originalGapAfterAiMessage = (message.from === 'ai' && (i + 1) < messagesToDisplay.length) ? originalChatLineHeight : 0;
                const originalTotalSpaceNeededForBlock = originalBlockEstimatedHeight + originalGapAfterAiMessage;

                if (textLines.length > 0 && (yPosition + originalTotalSpaceNeededForBlock) > (doc.internal.pageSize.getHeight() - topDocMargin) && yPosition !== topDocMargin) {
                    doc.addPage();
                    yPosition = topDocMargin;
                }

                // Original line-by-line rendering with its per-line page break logic
                textLines.forEach((line) => {
                    // If the space for the current line (using originalChatLineHeight) would overflow
                    if ((yPosition + originalChatLineHeight) > (doc.internal.pageSize.getHeight() - topDocMargin) && yPosition !== topDocMargin) {
                        doc.addPage();
                        yPosition = topDocMargin;
                    }
                    doc.text(line, margin, yPosition); // yPosition is the baseline
                    yPosition += originalChatLineHeight; // Increment to next baseline using original large spacing
                });

                // Original gap logic after an AI message
                if (message.from === 'ai' && (i + 1) < messagesToDisplay.length) {
                    // The original code just added this. If it caused overflow,
                    // the *next* message's block check would handle it.
                    yPosition += originalChatLineHeight;
                }
            }
        }


        // const feedbackText = "Quantum mechanics is a fascinating branch of physics that deals with the behavior of " +
        //     "very small particles like atoms and subatomic particles. It explores how these particles " +
        //     "can exist in multiple states at once and how they can exhibit both wave-like and " +
        //     "particle-like properties. If you have any specific questions, feel free to ask! " +
        //     "Quantum mechanics is a fascinating branch of physics that deals with the behavior of " +
        //     "very small particles";

        const feedbackText = feedback;

        const feedbackLines = splitTextIntoLines(feedbackText, contentMaxWidth - (2 * boxPadding));
        const feedbackTextHeight = feedbackLines.length * textLineHeightInBox;
        const feedbackBoxHeight = feedbackTextHeight + (3 * boxPadding);

        // Check if we need a new page for the feedback box
        if (yPosition + feedbackBoxHeight > doc.internal.pageSize.getHeight() - topDocMargin) {
            doc.addPage();
            yPosition = topDocMargin;
        }


        doc.setDrawColor(220, 220, 220);
        doc.setFillColor(220, 220, 220);
        doc.rect(margin, yPosition, contentMaxWidth, feedbackBoxHeight, 'FD');


        doc.setFont('helvetica', 'bold');
        doc.setFontSize(12);
        doc.setTextColor(50, 50, 50); // White text
        doc.text("Overall Feedback", margin + boxPadding, yPosition + boxPadding + 12);


        doc.setFont('helvetica', 'normal');
        doc.setFontSize(chatFontSize);
        let yFeedbackText = yPosition + boxPadding + 12 + textLineHeightInBox;

        feedbackLines.forEach(line => {
            doc.text(line, margin + boxPadding, yFeedbackText);
            yFeedbackText += textLineHeightInBox;
        });

        doc.save(`chat_export_${now.toISOString().split('T')[0]}.pdf`);
    };

    return (
        <>
            {showPreferenceModal && (
                <PreferenceModal
                    onClose={() => setShowPreferenceModal(false)}
                    onSubmit={(pref) => {
                        console.log("pref", pref);
                        setTopic(pref.topic);
                        // setLanguage(pref.language);
                        setUserPreference(pref);
                        setInitialPref(pref);
                        setShowPreferenceModal(false);
                        localStorage.setItem("userVivaPreference", JSON.stringify(pref));
                        startSession();
                    }}
                    initialTopic={initialPref.topic}
                    initialLanguage={initialPref.language}
                />
            )}

            {showResetModal && (
                <div className="fixed inset-0 bg-white/30 backdrop-blur-sm flex items-center justify-center z-50">
                    <div className="bg-white rounded-xl shadow-xl w-full max-w-md mx-4">
                        {/* Header */}
                        <div className="flex items-center justify-between px-6 py-4 rounded-t-xl"
                            style={{ background: 'linear-gradient(90deg, #1A39FF 0%, #A723FF 100%)' }}>
                            <span className="text-white font-medium text-lg">Reset Quiz</span>
                            <button
                                onClick={() => setShowResetModal(false)}
                                className="text-white text-xl hover:text-gray-200"
                            >
                                &times;
                            </button>
                        </div>

                        {/* Content */}
                        <div className="px-6 py-6">
                            <div className="text-center mb-6">
                                <div className="mb-4">
                                    <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-800 mb-2">Resetting the Quiz</h3>
                                    <p className="text-gray-600">
                                        This will reset your current interview session. You may want to download your chat before continuing.
                                    </p>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex gap-3">
                                <button
                                    onClick={() => setShowResetModal(false)}
                                    className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleConfirmReset}
                                    className="flex-1 px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 transition-colors font-medium"
                                >
                                    Continue
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}


            {!showPreferenceModal && (
                <div className="min-h-screen bg-white flex flex-col items-center justify-center py-8 px-2 md:px-0">
                    {/* Header */}
                    <div className={`w-full ${isMobile ? 'max-w-md' : 'max-w-5xl'} flex justify-between items-center mb-8`}>
                        <div className="flex items-center gap-8">
                            <div className={`text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 font-bold ${isMobile ? 'text-xl' : 'text-3xl md:text-4xl'}`} style={{ WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>
                                AI Interview - LIVE
                            </div>

                        </div>
                        <div className="flex gap-4 text-[#3734FF]">
                            <button onClick={downloadChatAsPDF}>
                                <MdOutlineDownloadForOffline className={`${isMobile ? 'w-6 h-6' : 'w-7 h-7'} rounded-full flex items-center justify-center text-lg hover:text-blue-600 cursor-pointer`} />
                            </button>
                            <button onClick={handleResetQuiz}>
                                <HiRefresh className={`${isMobile ? 'w-6 h-6' : 'w-7 h-7'} rounded-full flex items-center justify-center text-lg hover:text-blue-600 cursor-pointer`} />
                            </button>
                            <button onClick={() => navigate('/structured-breakdown')}>
                                <IoCloseCircleOutline className={`${isMobile ? 'w-6 h-6' : 'w-7 h-7'} rounded-full flex items-center justify-center text-lg`} />
                            </button>
                        </div>
                    </div>

                    {isMobile ? (
                        // Mobile/Tablet Layout - Video Call Style
                        <div className="w-full max-w-md border-1 border-gray-200 rounded-2xl p-4 flex flex-col gap-4 bg-[#D9D9D91A]">
                            {/* Main Video Container - Avatar with User Cam Overlay */}
                            <div className="relative w-full h-[60vh] rounded-2xl overflow-hidden border border-gray-200 shadow-lg bg-white">
                                {/* AI Avatar Video - Full Background */}
                                <video
                                    ref={avatarVideoRef}
                                    className="w-full h-full object-cover bg-white"
                                    autoPlay
                                    muted
                                />
                                <audio ref={avatarAudioRef} />

                                {/* User Camera Overlay - Top Left Corner */}
                                <div className="absolute top-4 left-4 w-24 h-32 rounded-lg overflow-hidden border-2 border-white shadow-lg bg-gray-100">
                                    {webcamOn ? (
                                        <video ref={videoRef} autoPlay muted className="object-cover w-full h-full" />
                                    ) : (
                                        <div className="w-full h-full flex flex-col items-center justify-center bg-gray-200">
                                            <PiUserFocusFill className="w-8 h-8 text-gray-400" />
                                            <div className="text-xs text-gray-600 text-center mt-1">Camera Off</div>
                                        </div>
                                    )}
                                </div>

                                {/* AI Message Overlay - Bottom */}
                                <div className="absolute bottom-4 left-4 right-4 bg-gradient-to-r from-[#6C9DFF] to-[#755CFF] rounded-lg px-3 py-2 text-white text-sm shadow-lg">
                                    {messages.filter(m => m.from === 'ai').slice(-1)[0]?.text}
                                </div>
                            </div>

                            {/* Controls Section */}
                            <div className="flex flex-col gap-4">
                                {/* Mic and Camera buttons */}
                                <div className="flex gap-6 justify-center">
                                    <button
                                        className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-xl shadow-md transition ${micActive ? 'bg-gradient-to-r from-[#A723FF] to-[#1A39FF]' : 'bg-red-600'}`}
                                        onClick={handleToggleMic}
                                    >
                                        <FaMicrophone className="w-5 h-5" />
                                    </button>
                                    <button
                                        className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-xl shadow-md transition ${webcamOn ? 'bg-gradient-to-r from-[#A723FF] to-[#1A39FF]' : 'bg-red-600'}`}
                                        onClick={handleToggleWebcam}
                                    >
                                        <IoIosVideocam className="w-5 h-5" />
                                    </button>
                                </div>

                                {/* Chat input */}
                                <div className="w-full flex items-center gap-2">
                                    <input
                                        className="flex-1 px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-200 focus:border-transparent text-base"
                                        type="text"
                                        placeholder="Your Text Here....."
                                        value={input}
                                        onChange={e => setInput(e.target.value)}
                                        onKeyDown={e => { if (e.key === 'Enter') handleSend(); }}
                                        disabled={micActive}
                                    />
                                    <button
                                        className={`w-10 h-10 rounded-full flex items-center justify-center  text-xl ${micActive ? 'bg-gray-300 text-gray-600 cursor-not-allowed' : 'bg-gradient-to-r from-purple-200 to-blue-200 text-gray-700 hover:text-purple-700 transition'}`}
                                        onClick={handleSend}
                                        disabled={micActive}
                                    >
                                        <IoSend className="w-5 h-5" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    ) : (
                        // Desktop Layout - Original Side by Side
                        <div className="w-full max-w-7xl border-1 border-gray-200 rounded-2xl p-6 flex flex-col md:flex-row gap-8 bg-[#D9D9D91A]">
                            {/* AI Cam Section */}
                            <div className="flex-1 flex flex-col items-center">
                                <div className="relative w-[70%] h-[100%] rounded-2xl overflow-hidden border border-gray-200 shadow-lg bg-white flex items-center justify-center mb-0">
                                    {/* AI Cam Image Placeholder */}
                                    <div className="absolute top-4 right-4 z-10">
                                        {/* Placeholder for waveform icon */}
                                        <span className="w-10 h-6 bg-blue-200 rounded-full flex items-center justify-center text-blue-700 font-bold">〰️</span>
                                    </div>
                                    {/* <img src="https://www.indianext.co.in/wp-content/uploads/2022/03/portrait-female-teacher-holding-notepad-green.jpg" alt="AI Cam" className="object-cover w-full h-full" /> */}
                                    <video
                                        ref={avatarVideoRef}
                                        className="w-full h-full object-cover bg-white"
                                        autoPlay
                                        muted
                                    />
                                    <audio ref={avatarAudioRef} />
                                    {/* AI message overlay */}
                                    <div className="absolute bottom-4 left-1/2 -translate-x-1/2 w-[90%] bg-gradient-to-r from-[#6C9DFF] to-[#755CFF] rounded-lg px-4 py-2 text-white text-sm shadow-lg">
                                        {messages.filter(m => m.from === 'ai').slice(-1)[0]?.text}
                                    </div>
                                </div>
                            </div>

                            {/* User Cam & Chat Section */}
                            <div className="flex-1 flex flex-col items-center">
                                {/*<button onClick={processMessagesForAPI}>test</button>*/}
                                <div className="w-[90%] h-[400px] rounded-2xl overflow-hidden border border-gray-200 shadow-lg bg-gray-100 flex items-center justify-center mb-0">
                                    {webcamOn ? (
                                        <video ref={videoRef} autoPlay muted className="object-cover w-full h-full" />
                                    ) : (
                                        <div className="w-full h-full flex flex-col items-center justify-center">
                                            {/* <FaUserCircle className="w-1/3 h-1/3 text-gray-400" /> */}
                                            <PiUserFocusFill className="w-1/3 h-1/3 text-gray-400" />
                                            <div className="w-full flex items-center justify-center text-gray-600 text-2xl">Camera Turned Off</div>
                                        </div>
                                    )}
                                </div>
                                {/* Mic and Camera buttons below video */}
                                <div className="flex gap-6 mt-4 mb-4 justify-center">
                                    <button
                                        className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-2xl shadow-md transition ${micActive ? 'bg-gradient-to-r from-[#A723FF] to-[#1A39FF]' : 'bg-red-600'}`}
                                        onClick={handleToggleMic}
                                    >
                                        <FaMicrophone className="w-6 h-6" />
                                    </button>
                                    <button
                                        className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-2xl shadow-md transition ${webcamOn ? 'bg-gradient-to-r from-[#A723FF] to-[#1A39FF]' : 'bg-red-600'}`}
                                        onClick={handleToggleWebcam}
                                    >
                                        <IoIosVideocam className="w-6 h-6" />
                                    </button>
                                </div>
                                {/* Chat input below buttons */}
                                <div className="w-full flex items-center gap-2 mt-2">
                                    <input
                                        className="flex-1 px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-200 focus:border-transparent text-base"
                                        type="text"
                                        placeholder="Your Text Here....."
                                        value={input}
                                        onChange={e => setInput(e.target.value)}
                                        onKeyDown={e => { if (e.key === 'Enter') handleSend(); }}
                                        disabled={micActive}
                                    />
                                    <button
                                        className={`w-10 h-10 rounded-full flex items-center justify-center  text-xl ${micActive ? 'bg-gray-300 text-gray-600 cursor-not-allowed' : 'bg-gradient-to-r from-purple-200 to-blue-200 text-gray-700 hover:text-purple-700 transition'}`}
                                        onClick={handleSend}
                                        disabled={micActive}
                                    >
                                        <IoSend className="w-6 h-6" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </>
    );
};

export default Viva;