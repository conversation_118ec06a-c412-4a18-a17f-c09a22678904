import React from 'react';

const InsufficientBalanceAlert = ({
  isOpen,
  onClose,
  onBuyPlan,
  title = "Oops !",
  message = "Insufficient Balance!\nCannot Perform This Action Now.",
  buttonText = "Buy a Plan"
}) => {
  if (!isOpen) return null;

  const handleBuyPlan = () => {
    if (onBuyPlan) {
      onBuyPlan();
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div
        className="relative w-full max-w-[586px] h-[388px] rounded-lg shadow-2xl"
        style={{
          background: 'linear-gradient(135deg, #EEF2FF 0%, #FAF5FF 100%)',
          border: '0.5px solid',
          borderImage: 'linear-gradient(180deg, rgba(202, 204, 216, 0.4) 0%, rgba(75, 85, 99, 0.5) 100%) 1',
          boxShadow: '2.125px 4.25px 8.5px 0px rgba(0, 0, 0, 0.2)'
        }}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-[27px] right-[32px] w-8 h-8 flex items-center justify-center hover:opacity-70 transition-opacity z-10"
          aria-label="Close alert"
        >
          <img
            src="/images/close-icon.png"
            alt="Close"
            className="w-8 h-8"
          />
        </button>

        {/* Content Container */}
        <div className="flex flex-col items-center justify-center h-full px-12 py-8">
          {/* Title */}
          <div className="mb-8">
            <h2
              className="text-center font-bold"
              style={{
                fontFamily: 'Helvetica',
                fontSize: '50px',
                lineHeight: '0.64em',
                color: '#3A1078'
              }}
            >
              {title}
            </h2>
          </div>

          {/* Message */}
          <div className="mb-12 px-6">
            <p
              className="text-center whitespace-pre-line"
              style={{
                fontFamily: 'Inter',
                fontSize: '28px',
                lineHeight: '1.6428571428571428em',
                color: '#4B5563',
                width: '490px',
                maxWidth: '100%'
              }}
            >
              {message}
            </p>
          </div>

          {/* Buy a Plan Button */}
          <button
            onClick={handleBuyPlan}
            className="px-14 py-2 rounded-lg text-white font-medium transition-all duration-200 hover:opacity-90 hover:scale-105 active:scale-95"
            style={{
              background: 'linear-gradient(90deg, #6BA0FF 0%, #755BFF 100%)',
              fontFamily: 'Inter',
              fontSize: '20.705204010009766px',
              lineHeight: '1.499999907880713em',
              fontWeight: '500',
              minWidth: '197px',
              height: 'auto',
              padding: '9.202312469482422px 55.21387481689453px'
            }}
          >
            {buttonText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default InsufficientBalanceAlert;
